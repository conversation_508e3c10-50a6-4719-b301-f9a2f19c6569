#!/usr/bin/env python3
"""
创建优化的启动器
支持路径自适应和错误处理
"""
import os
import sys
from pathlib import Path

def create_launcher():
    """创建启动器脚本"""
    
    launcher_content = '''#!/usr/bin/env python3
"""
公司制度查询平台 - 智能启动器
支持路径自适应、错误恢复和性能优化
"""
import os
import sys
import subprocess
import time
from pathlib import Path

class SmartLauncher:
    """智能启动器"""
    
    def __init__(self):
        # 获取程序根目录
        if getattr(sys, 'frozen', False):
            # 打包环境
            self.app_root = Path(sys.executable).parent
        else:
            # 开发环境
            self.app_root = Path(__file__).parent
        
        self.python_dir = self.app_root / "python"
        self.app_dir = self.app_root / "app"
        self.python_exe = self.python_dir / "python.exe"
        self.main_py = self.app_dir / "main.py"
    
    def check_environment(self):
        """检查运行环境"""
        print("🔍 检查运行环境...")
        
        # 检查Python解释器
        if not self.python_exe.exists():
            self.show_error(f"Python解释器不存在: {self.python_exe}")
            return False
        
        # 检查主程序
        if not self.main_py.exists():
            self.show_error(f"主程序不存在: {self.main_py}")
            return False
        
        # 检查关键目录
        required_dirs = [
            self.app_dir / "src",
            self.app_dir / "data",
            self.app_dir / "models"
        ]
        
        for dir_path in required_dirs:
            if not dir_path.exists():
                print(f"⚠️ 创建缺失目录: {dir_path}")
                dir_path.mkdir(parents=True, exist_ok=True)
        
        return True
    
    def setup_environment(self):
        """设置环境变量"""
        print("⚙️ 设置环境变量...")
        
        # Python环境
        os.environ["PYTHONHOME"] = str(self.python_dir)
        os.environ["PYTHONPATH"] = str(self.app_dir)
        
        # 应用程序路径
        os.environ["APP_ROOT"] = str(self.app_root)
        os.environ["APP_DIR"] = str(self.app_dir)
        
        # 性能优化
        cpu_count = os.cpu_count() or 4
        optimal_threads = min(cpu_count, 8)
        
        env_vars = {
            'OMP_NUM_THREADS': str(optimal_threads),
            'MKL_NUM_THREADS': str(optimal_threads),
            'NUMEXPR_NUM_THREADS': str(optimal_threads),
            'OPENBLAS_NUM_THREADS': str(optimal_threads),
            'TORCH_NUM_THREADS': str(optimal_threads),
            'MKL_ENABLE_INSTRUCTIONS': 'AVX2'
        }
        
        for var, value in env_vars.items():
            os.environ[var] = value
        
        print(f"   使用 {optimal_threads} 个线程进行优化")
    
    def check_models(self):
        """检查模型文件"""
        print("🤖 检查AI模型...")
        
        models_dir = self.app_dir / "models"
        required_models = [
            "qwen-1.5-1.8b-chat",
            "shibing624"
        ]
        
        missing_models = []
        for model_name in required_models:
            model_path = models_dir / model_name
            if not model_path.exists() or not any(model_path.iterdir()):
                missing_models.append(model_name)
        
        if missing_models:
            print(f"⚠️ 缺失模型: {', '.join(missing_models)}")
            print("   程序将在首次运行时自动下载模型")
        else:
            print("✅ 所有模型文件完整")
        
        return True
    
    def launch_application(self):
        """启动应用程序"""
        print("🚀 启动应用程序...")
        
        try:
            # 切换到应用目录
            os.chdir(self.app_dir)
            
            # 启动应用
            cmd = [str(self.python_exe), str(self.main_py)]
            
            print(f"   执行命令: {' '.join(cmd)}")
            print(f"   工作目录: {self.app_dir}")
            
            # 使用subprocess启动，保持控制台输出
            process = subprocess.Popen(
                cmd,
                cwd=str(self.app_dir),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 实时输出日志
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    print(output.strip())
            
            return_code = process.poll()
            if return_code != 0:
                print(f"❌ 程序异常退出，返回码: {return_code}")
                return False
            
            return True
            
        except Exception as e:
            self.show_error(f"启动失败: {e}")
            return False
    
    def show_error(self, message):
        """显示错误信息"""
        print(f"❌ 错误: {message}")
        
        # 尝试显示图形化错误对话框
        try:
            import tkinter as tk
            from tkinter import messagebox
            
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            messagebox.showerror("启动错误", message)
            root.destroy()
        except:
            # 如果tkinter不可用，只在控制台显示
            pass
    
    def run(self):
        """运行启动器"""
        print("=" * 50)
        print("🏢 公司制度查询平台")
        print("=" * 50)
        
        try:
            # 检查环境
            if not self.check_environment():
                input("按回车键退出...")
                return False
            
            # 设置环境
            self.setup_environment()
            
            # 检查模型
            self.check_models()
            
            # 启动应用
            success = self.launch_application()
            
            if not success:
                input("按回车键退出...")
                return False
            
            return True
            
        except KeyboardInterrupt:
            print("\\n👋 用户中断，程序退出")
            return True
        except Exception as e:
            self.show_error(f"启动器异常: {e}")
            input("按回车键退出...")
            return False

def main():
    """主函数"""
    launcher = SmartLauncher()
    launcher.run()

if __name__ == "__main__":
    main()
'''
    
    # 保存启动器脚本
    launcher_path = Path("smart_launcher.py")
    launcher_path.write_text(launcher_content, encoding='utf-8')
    print(f"✅ 启动器脚本已创建: {launcher_path}")
    
    # 创建批处理启动器
    batch_content = '''@echo off
chcp 65001 > nul
title 公司制度查询平台

echo 正在启动公司制度查询平台...
python smart_launcher.py

if errorlevel 1 (
    echo.
    echo 启动失败，请检查错误信息
    pause
)
'''
    
    batch_path = Path("start_app.bat")
    batch_path.write_text(batch_content, encoding='utf-8')
    print(f"✅ 批处理启动器已创建: {batch_path}")

if __name__ == "__main__":
    create_launcher()
