#!/usr/bin/env python3
"""
修复编码问题 - 替换表情符号为文本标记
"""
import os
import re
from pathlib import Path

def fix_emoji_encoding():
    """修复表情符号编码问题"""
    
    # 表情符号映射
    emoji_map = {
        '🔍': '[检查]',
        '📦': '[打包]',
        '⚙️': '[配置]',
        '🚀': '[启动]',
        '💿': '[安装]',
        '📁': '[文件]',
        '📊': '[报告]',
        '🎉': '[完成]',
        '❌': '[错误]',
        '✅': '[成功]',
        '🏗️': '[构建]',
        '📥': '[下载]',
        '🤖': '[模型]',
        '⏱️': '[时间]',
        '🧪': '[测试]',
        '📖': '[帮助]',
        '🐍': '[Python]',
        '💾': '[数据]',
        '🏃': '[运行]',
        'ℹ️': '[信息]',
        '🔧': '[工具]'
    }
    
    # 需要修复的文件
    files_to_fix = [
        'build_complete.py',
        'optimize_config.py',
        'create_launcher.py',
        'test_package.py',
        'quick_start.py'
    ]
    
    for file_path in files_to_fix:
        if not Path(file_path).exists():
            print(f"跳过不存在的文件: {file_path}")
            continue
            
        print(f"修复文件: {file_path}")
        
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换表情符号
        for emoji, replacement in emoji_map.items():
            content = content.replace(emoji, replacement)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"  已修复: {file_path}")
    
    print("所有文件修复完成！")

if __name__ == "__main__":
    fix_emoji_encoding()
