#!/usr/bin/env python3
"""
公司制度查询平台 - 零配置打包脚本
实现嵌入式Python + NSIS安装器的打包方案
"""
import os
import sys
import shutil
import subprocess
import zipfile
import json
from pathlib import Path
from typing import Dict, List, Optional
import urllib.request
import tempfile

class PackageBuilder:
    """打包构建器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.build_dir = self.project_root / "build_package"
        self.dist_dir = self.project_root / "dist"
        self.python_version = "3.10.11"
        self.app_name = "CompanyPolicyQA"
        self.app_version = "1.0.0"
        
        # 打包配置
        self.config = {
            "python_url": f"https://www.python.org/ftp/python/{self.python_version}/python-{self.python_version}-embed-amd64.zip",
            "pip_url": "https://bootstrap.pypa.io/get-pip.py",
            "required_packages": [
                "torch==2.1.0+cpu",
                "transformers==4.37.0", 
                "sentence-transformers==4.0.0",
                "chromadb==0.4.24",
                "whoosh==2.7.4",
                "PyQt6==6.6.0",
                "PyQt6-WebEngine==6.6.0",
                "PyMuPDF==1.23.26",
                "python-docx==1.1.0",
                "openpyxl==3.1.2",
                "pytesseract==0.3.10",
                "pdf2image==1.16.3",
                "Pillow==10.1.0",
                "jieba==0.42.1",
                "zhconv==1.4.3",
                "numpy==1.24.4",
                "pandas==2.1.4",
                "tqdm==4.66.1",
                "requests==2.31.0",
                "pydantic==2.5.0",
                "psutil==5.9.6",
                "loguru==0.7.2",
                "pyyaml==6.0.1",
                "python-dotenv==1.0.0"
            ],
            "exclude_dirs": [
                "__pycache__",
                ".git",
                ".pytest_cache",
                "build",
                "dist",
                "*.egg-info",
                ".vscode",
                ".idea"
            ]
        }
    
    def clean_build_dir(self):
        """清理构建目录"""
        print("[清理] 清理构建目录...")
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
        self.build_dir.mkdir(parents=True, exist_ok=True)
        
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
        self.dist_dir.mkdir(parents=True, exist_ok=True)
    
    def download_embedded_python(self):
        """下载嵌入式Python"""
        print(f"[下载] 下载嵌入式Python {self.python_version}...")

        python_dir = self.build_dir / "python"
        python_zip = self.build_dir / f"python-{self.python_version}-embed-amd64.zip"
        
        if not python_zip.exists():
            print(f"   从 {self.config['python_url']} 下载...")
            urllib.request.urlretrieve(self.config['python_url'], python_zip)
        
        # 解压Python
        print("   解压Python...")
        with zipfile.ZipFile(python_zip, 'r') as zip_ref:
            zip_ref.extractall(python_dir)
        
        # 修改python310._pth文件以启用site-packages
        pth_file = python_dir / f"python{self.python_version.replace('.', '')[:3]}._pth"
        if pth_file.exists():
            content = pth_file.read_text()
            if "#import site" in content:
                content = content.replace("#import site", "import site")
                pth_file.write_text(content)
        
        return python_dir
    
    def install_pip(self, python_dir: Path):
        """安装pip"""
        print("[安装] 安装pip...")

        get_pip_py = self.build_dir / "get-pip.py"
        if not get_pip_py.exists():
            urllib.request.urlretrieve(self.config['pip_url'], get_pip_py)
        
        python_exe = python_dir / "python.exe"
        subprocess.run([str(python_exe), str(get_pip_py)], check=True)
    
    def install_packages(self, python_dir: Path):
        """安装Python包"""
        print("[安装] 安装Python包...")

        python_exe = python_dir / "python.exe"
        
        # 安装PyTorch CPU版本
        print("   安装PyTorch (CPU版本)...")
        subprocess.run([
            str(python_exe), "-m", "pip", "install", 
            "torch==2.1.0+cpu", "torchvision==0.16.0+cpu", "torchaudio==2.1.0+cpu",
            "--index-url", "https://download.pytorch.org/whl/cpu"
        ], check=True)
        
        # 安装其他包
        for package in self.config['required_packages']:
            if not package.startswith("torch"):  # 跳过torch，已经安装
                print(f"   安装 {package}...")
                subprocess.run([
                    str(python_exe), "-m", "pip", "install", package
                ], check=True)
    
    def copy_application(self):
        """复制应用程序文件"""
        print("[复制] 复制应用程序文件...")

        app_dir = self.build_dir / "app"
        app_dir.mkdir(exist_ok=True)
        
        # 复制源代码
        src_files = [
            "main.py",
            "preprocess.py", 
            "config.yaml",
            "requirements.txt",
            "src/",
            "static/"
        ]
        
        for item in src_files:
            src_path = self.project_root / item
            if src_path.exists():
                if src_path.is_dir():
                    shutil.copytree(src_path, app_dir / item, 
                                  ignore=shutil.ignore_patterns(*self.config['exclude_dirs']))
                else:
                    shutil.copy2(src_path, app_dir / item)
        
        # 创建数据目录结构
        data_dirs = ["data", "docs", "models", "logs"]
        for dir_name in data_dirs:
            (app_dir / dir_name).mkdir(exist_ok=True)
    
    def copy_models(self):
        """复制AI模型文件"""
        print("[模型] 复制AI模型文件...")
        
        models_src = self.project_root / "models"
        models_dst = self.build_dir / "app" / "models"
        
        if models_src.exists():
            # 复制模型文件，但跳过缓存文件
            for model_dir in models_src.iterdir():
                if model_dir.is_dir():
                    dst_model_dir = models_dst / model_dir.name
                    dst_model_dir.mkdir(parents=True, exist_ok=True)
                    
                    for file_path in model_dir.rglob("*"):
                        if file_path.is_file() and not any(
                            exclude in str(file_path) for exclude in ["__pycache__", ".git", "cache"]
                        ):
                            rel_path = file_path.relative_to(model_dir)
                            dst_file = dst_model_dir / rel_path
                            dst_file.parent.mkdir(parents=True, exist_ok=True)
                            shutil.copy2(file_path, dst_file)
    
    def copy_data(self):
        """复制数据文件"""
        print("[数据] 复制数据文件...")
        
        data_src = self.project_root / "data"
        data_dst = self.build_dir / "app" / "data"
        
        if data_src.exists():
            # 复制向量数据库和索引
            for item in ["chroma_db", "whoosh_index"]:
                src_path = data_src / item
                if src_path.exists():
                    shutil.copytree(src_path, data_dst / item, dirs_exist_ok=True)
        
        # 复制文档
        docs_src = self.project_root / "docs"
        docs_dst = self.build_dir / "app" / "docs"
        
        if docs_src.exists():
            shutil.copytree(docs_src, docs_dst, dirs_exist_ok=True)
    
    def create_launcher(self):
        """创建启动器"""
        print("[创建] 创建启动器...")

        launcher_script = self.build_dir / "launcher.py"
        launcher_content = '''#!/usr/bin/env python3
"""
公司制度查询平台启动器
"""
import os
import sys
from pathlib import Path

def main():
    # 获取程序目录
    if getattr(sys, 'frozen', False):
        app_dir = Path(sys.executable).parent
    else:
        app_dir = Path(__file__).parent
    
    # 设置Python路径
    python_dir = app_dir / "python"
    python_exe = python_dir / "python.exe"
    
    # 设置环境变量
    os.environ["PYTHONPATH"] = str(app_dir / "app")
    os.environ["PYTHONHOME"] = str(python_dir)
    
    # 启动应用
    main_py = app_dir / "app" / "main.py"
    os.execv(str(python_exe), [str(python_exe), str(main_py)])

if __name__ == "__main__":
    main()
'''
        launcher_script.write_text(launcher_content, encoding='utf-8')
    
    def optimize_package(self):
        """优化打包结果"""
        print("[优化] 优化打包结果...")
        
        python_dir = self.build_dir / "python"
        
        # 删除不需要的文件
        unnecessary_files = [
            "*.pyc",
            "__pycache__",
            "*.pyo", 
            "test/",
            "tests/",
            "*.dist-info/RECORD",
            "*.dist-info/WHEEL"
        ]
        
        for pattern in unnecessary_files:
            for file_path in python_dir.rglob(pattern):
                if file_path.is_file():
                    file_path.unlink()
                elif file_path.is_dir():
                    shutil.rmtree(file_path, ignore_errors=True)
    
    def create_config(self):
        """创建打包配置"""
        print("[配置] 创建配置文件...")

        # 修改应用配置为绝对路径
        app_config = self.build_dir / "app" / "config.yaml"
        if app_config.exists():
            content = app_config.read_text(encoding='utf-8')
            # 这里可以添加路径修改逻辑
            app_config.write_text(content, encoding='utf-8')
    
    def build(self):
        """执行完整构建"""
        print(f"[构建] 开始构建 {self.app_name} v{self.app_version}")

        try:
            self.clean_build_dir()
            python_dir = self.download_embedded_python()
            self.install_pip(python_dir)
            self.install_packages(python_dir)
            self.copy_application()
            self.copy_models()
            self.copy_data()
            self.create_launcher()
            self.create_config()
            self.optimize_package()

            print(f"[完成] 构建完成！输出目录: {self.build_dir}")
            print(f"[信息] 包大小: {self._get_dir_size(self.build_dir):.1f} MB")

        except Exception as e:
            print(f"[错误] 构建失败: {e}")
            raise
    
    def _get_dir_size(self, path: Path) -> float:
        """获取目录大小(MB)"""
        total_size = 0
        for file_path in path.rglob("*"):
            if file_path.is_file():
                total_size += file_path.stat().st_size
        return total_size / (1024 * 1024)

if __name__ == "__main__":
    builder = PackageBuilder()
    builder.build()
