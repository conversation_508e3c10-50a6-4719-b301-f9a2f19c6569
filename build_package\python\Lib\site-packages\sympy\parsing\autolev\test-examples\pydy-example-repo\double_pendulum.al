MOTIONVARIABLES' Q{2}', U{2}'
CONSTANTS L,M,G
NEWTONIAN N
FRAMES A,B
SIMPROT(N, A, 3, Q1)
SIMPROT(N, B, 3, Q2)
W_A_N>=U1*N3>
W_B_N>=U2*N3>
POINT O
PARTICLES P,R
P_O_P> = L*A1>
P_P_R> = L*B1>
V_O_N> = 0>
V2PTS(N, A, O, P)
V2PTS(N, B, P, R)
MASS P=M, R=M
Q1' = U1
Q2' = U2
GRAVITY(G*N1>)
ZERO = FR() + FRSTAR()
KANE()
INPUT M=1,G=9.81,L=1
INPUT Q1=.1,Q2=.2,U1=0,U2=0
INPUT TFINAL=10, INTEGSTP=.01
CODE DYNAMICS() some_filename.c
