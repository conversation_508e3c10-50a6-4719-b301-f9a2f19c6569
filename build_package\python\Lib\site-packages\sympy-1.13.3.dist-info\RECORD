../../Scripts/isympy.exe,sha256=DR5auQj1A43NeyUTegsgjqyUXYdSmCGdR5ZfNE3jsXM,108383
../../share/man/man1/isympy.1,sha256=9DZdSOIQLikrATHlbkdDZ04LBQigZDUE0_oCXBDvdBs,6659
__pycache__/isympy.cpython-310.pyc,,
isympy.py,sha256=gAoHa7OM0y9G5IBO7wO-uTpD-CPnd6sbmjJ_GGB0yzg,11207
sympy-1.13.3.dist-info/AUTHORS,sha256=LU7_WXv-LYwodkUQMT_Xc7n9BVkcoDbRfeoXvn3HrvQ,53081
sympy-1.13.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
sympy-1.13.3.dist-info/LICENSE,sha256=B6XpgZ9ye0mGrSgpx6KaYyDUJXX3IOsk1xt_71c6AoY,7885
sympy-1.13.3.dist-info/METADATA,sha256=ffDGKMJ6B2rquVLX5fpg55XT4dCT6xIwHVZh3W_WuVQ,12470
sympy-1.13.3.dist-info/RECORD,,
sympy-1.13.3.dist-info/WHEEL,sha256=GV9aMThwP_4oNCtvEC2ec3qUYutgWeAzklro_0m4WJQ,91
sympy-1.13.3.dist-info/entry_points.txt,sha256=Sp-vLJom4PRlhGfY6RpUre7SjYm33JNq9NCwCGeW-fQ,39
sympy-1.13.3.dist-info/top_level.txt,sha256=elXb5xfjLdjgSSoQFk4_2Qu3lp2CIaglF9MQtfIoH7o,13
sympy/__init__.py,sha256=r30XFqXM3yHgd75Ui-_Zhun88KFbeRTAr-88EEvsK5Y,29271
sympy/__pycache__/__init__.cpython-310.pyc,,
sympy/__pycache__/abc.cpython-310.pyc,,
sympy/__pycache__/conftest.cpython-310.pyc,,
sympy/__pycache__/galgebra.cpython-310.pyc,,
sympy/__pycache__/release.cpython-310.pyc,,
sympy/__pycache__/this.cpython-310.pyc,,
sympy/abc.py,sha256=P1iQKfXl7Iut6Z5Y97QmGr_UqiAZ6qR-eoRMtYacGfA,3748
sympy/algebras/__init__.py,sha256=7PRGOW30nlMOTeUPR7iy8l5xGoE2yCBEfRbjqDKWOgU,62
sympy/algebras/__pycache__/__init__.cpython-310.pyc,,
sympy/algebras/__pycache__/quaternion.cpython-310.pyc,,
sympy/algebras/quaternion.py,sha256=szb4nf8HEiS5iiwSrXp5mRusfiDMTuc8-Q6lMKuBrkc,47527
sympy/algebras/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/algebras/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/algebras/tests/__pycache__/test_quaternion.cpython-310.pyc,,
sympy/algebras/tests/test_quaternion.py,sha256=2-LYu7LxyPQ2hZroasMRq4l6I2o-jICcf-Efhj82At0,16445
sympy/assumptions/__init__.py,sha256=PFS8djTqiNbGVMjg7PaPjEfwmjyZVfioXiRVzqqA3E0,550
sympy/assumptions/__pycache__/__init__.cpython-310.pyc,,
sympy/assumptions/__pycache__/ask.cpython-310.pyc,,
sympy/assumptions/__pycache__/ask_generated.cpython-310.pyc,,
sympy/assumptions/__pycache__/assume.cpython-310.pyc,,
sympy/assumptions/__pycache__/cnf.cpython-310.pyc,,
sympy/assumptions/__pycache__/facts.cpython-310.pyc,,
sympy/assumptions/__pycache__/lra_satask.cpython-310.pyc,,
sympy/assumptions/__pycache__/refine.cpython-310.pyc,,
sympy/assumptions/__pycache__/satask.cpython-310.pyc,,
sympy/assumptions/__pycache__/sathandlers.cpython-310.pyc,,
sympy/assumptions/__pycache__/wrapper.cpython-310.pyc,,
sympy/assumptions/ask.py,sha256=B2W8CW-9ynvYqfBX9_4qCLhCSwE0g_gUGqq7lM50wUk,19376
sympy/assumptions/ask_generated.py,sha256=whNIU5tj2UkEGHPAfk-_89ahvVERs4npOB0JYVbIQJc,23558
sympy/assumptions/assume.py,sha256=_gcFc4h_YGs9-tshoD0gmLl_RtPivDQWMWhWWLX9seo,14606
sympy/assumptions/cnf.py,sha256=mfthFXL8Jhpn7Vgj1IfG5acFzfBgaV02xztQ7KpoLmg,12509
sympy/assumptions/facts.py,sha256=fimPoHEyusSUr0uI4kiDb4mzxHjEBglvLQW0DGdNFAs,8391
sympy/assumptions/handlers/__init__.py,sha256=lvjAfPdz0MDjTxjuzbBSGBco2OmpZRiGixSG0oaiZi0,330
sympy/assumptions/handlers/__pycache__/__init__.cpython-310.pyc,,
sympy/assumptions/handlers/__pycache__/calculus.cpython-310.pyc,,
sympy/assumptions/handlers/__pycache__/common.cpython-310.pyc,,
sympy/assumptions/handlers/__pycache__/matrices.cpython-310.pyc,,
sympy/assumptions/handlers/__pycache__/ntheory.cpython-310.pyc,,
sympy/assumptions/handlers/__pycache__/order.cpython-310.pyc,,
sympy/assumptions/handlers/__pycache__/sets.cpython-310.pyc,,
sympy/assumptions/handlers/calculus.py,sha256=ul36wLjxrU_LUxEWX63dWklWHgHWw5xVT0d7BkZCdFE,7198
sympy/assumptions/handlers/common.py,sha256=sW_viw2xdO9Klqf31x3YlYcGlhgRj52HV1JFmwrgtb4,4064
sympy/assumptions/handlers/matrices.py,sha256=Gdauk2xk1hKPRr4i6RpvOMHtDnyVD34x1OyhL-Oh8Hc,22321
sympy/assumptions/handlers/ntheory.py,sha256=wl5cHhFgEHpJIi4QFWDJJJkbST6riMsBPd--Neoa-k8,7267
sympy/assumptions/handlers/order.py,sha256=Y6Txiykbj4gkibX0mrcUUlhtRWE27p-4lpG4WACX3Ik,12222
sympy/assumptions/handlers/sets.py,sha256=2Jh2G6Ce1qz9Imzv5et_v-sMxY62j3rFdnp1UZ_PGB8,23818
sympy/assumptions/lra_satask.py,sha256=FlmiLERsj6J9w6vygwEEEn7pyGPnD0JkPEFEdoE7bfM,9563
sympy/assumptions/predicates/__init__.py,sha256=q1C7iWpvdDymEUZNyzJvZLsLtgwSkYtCixME-fYyIDw,110
sympy/assumptions/predicates/__pycache__/__init__.cpython-310.pyc,,
sympy/assumptions/predicates/__pycache__/calculus.cpython-310.pyc,,
sympy/assumptions/predicates/__pycache__/common.cpython-310.pyc,,
sympy/assumptions/predicates/__pycache__/matrices.cpython-310.pyc,,
sympy/assumptions/predicates/__pycache__/ntheory.cpython-310.pyc,,
sympy/assumptions/predicates/__pycache__/order.cpython-310.pyc,,
sympy/assumptions/predicates/__pycache__/sets.cpython-310.pyc,,
sympy/assumptions/predicates/calculus.py,sha256=vFnlYVYZVd6D9OwA7-3bDK_Q0jf2iCZCZiMlWenw0Vg,1889
sympy/assumptions/predicates/common.py,sha256=zpByACpa_tF0nVNB0J_rJehnXkHtkxhchn1DvkVVS-s,2279
sympy/assumptions/predicates/matrices.py,sha256=X3vbkEf3zwJLyanEjf6ijYXuRfFfSv-yatl1tJ25wDk,12142
sympy/assumptions/predicates/ntheory.py,sha256=wvFNFSf0S4egbY7REw0V0ANC03CuiRU9PLmdi16VfHo,2546
sympy/assumptions/predicates/order.py,sha256=ez1UZ824KDtimLssUASCZHD_KEQmo8Pv-qofVLhZUrk,9511
sympy/assumptions/predicates/sets.py,sha256=-bTVXa-X1-yfXlIKzMBW_JxIqueS5PdEwEzChzIne38,9238
sympy/assumptions/refine.py,sha256=FNv5neAYJh-MgvnHDZ8-tjC9RIKcIxarVj5WiE4EnYg,11946
sympy/assumptions/relation/__init__.py,sha256=t2tZNEIK7w-xXshRQIRL8tIyiNe1W5fMhN7QNRPnQFo,261
sympy/assumptions/relation/__pycache__/__init__.cpython-310.pyc,,
sympy/assumptions/relation/__pycache__/binrel.cpython-310.pyc,,
sympy/assumptions/relation/__pycache__/equality.cpython-310.pyc,,
sympy/assumptions/relation/binrel.py,sha256=3iwnSEE53-vRsPv-bOnjydgOkCpbB12FTFR_sQ3CwvE,6313
sympy/assumptions/relation/equality.py,sha256=ZBnSFpctNeroYy1nvam0kzSJCNpsUR3SlBeqFcAMM0U,7148
sympy/assumptions/satask.py,sha256=P3iprPjuOyhT5Fwr0hX61xTOcD98M_bzXSAV-pXYhN4,11745
sympy/assumptions/sathandlers.py,sha256=jx8B0u_N73fMoVoLKIfmXMdtSLz7-ZIKhJrxYl84AJk,9418
sympy/assumptions/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/assumptions/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/assumptions/tests/__pycache__/test_assumptions_2.cpython-310.pyc,,
sympy/assumptions/tests/__pycache__/test_context.cpython-310.pyc,,
sympy/assumptions/tests/__pycache__/test_matrices.cpython-310.pyc,,
sympy/assumptions/tests/__pycache__/test_query.cpython-310.pyc,,
sympy/assumptions/tests/__pycache__/test_refine.cpython-310.pyc,,
sympy/assumptions/tests/__pycache__/test_rel_queries.cpython-310.pyc,,
sympy/assumptions/tests/__pycache__/test_satask.cpython-310.pyc,,
sympy/assumptions/tests/__pycache__/test_sathandlers.cpython-310.pyc,,
sympy/assumptions/tests/__pycache__/test_wrapper.cpython-310.pyc,,
sympy/assumptions/tests/test_assumptions_2.py,sha256=oNgIDOoW-GpBbXxbtw05SWnE8I7sGislYmB3MDogwB4,1070
sympy/assumptions/tests/test_context.py,sha256=I5gES7AY9_vz1-CEaCchy4MXABtX85ncNkvoRuLskG8,1153
sympy/assumptions/tests/test_matrices.py,sha256=nzSofuawc18hNe9Nj0dN_lTeDwa2KbPjt4K2rvb3xmw,12258
sympy/assumptions/tests/test_query.py,sha256=TU5cH4Cn_IGV4xDN5JImYGz_vAft5lqES2Rx62ZwdrQ,98387
sympy/assumptions/tests/test_refine.py,sha256=bHxYUnCOEIzA1yPU3B2xbU9JZfhDv6RkmPm8esetisQ,8834
sympy/assumptions/tests/test_rel_queries.py,sha256=C29emWaT1zT-7KGn235aYeytBaIlFMwlfiFsVo9wx-8,6676
sympy/assumptions/tests/test_satask.py,sha256=IIqqIxzkLfANpTNBKEsCGCp3Bm8zmDnYd23woqKh9EE,15741
sympy/assumptions/tests/test_sathandlers.py,sha256=jMCZQb3G6pVQ5MHaSTWV_0eULHaCF8Mowu12Ll72rgs,1842
sympy/assumptions/tests/test_wrapper.py,sha256=iE32j83rrerCz85HHt2hTolgJkqb44KddfEpI3H1Fb8,1159
sympy/assumptions/wrapper.py,sha256=7GXR39zPCCfV-pcs8ph9KRRwZF3i_T5Lzv156vKFf_I,5434
sympy/benchmarks/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/benchmarks/__pycache__/__init__.cpython-310.pyc,,
sympy/benchmarks/__pycache__/bench_discrete_log.cpython-310.pyc,,
sympy/benchmarks/__pycache__/bench_meijerint.cpython-310.pyc,,
sympy/benchmarks/__pycache__/bench_symbench.cpython-310.pyc,,
sympy/benchmarks/bench_discrete_log.py,sha256=CNchIJ5HFMPpNlVZh2vOU0GgQ3bse6hqyqDovpDHlKE,2473
sympy/benchmarks/bench_meijerint.py,sha256=dSNdZhoc8a4h50wRtbOxLwpmgUiuMFpe6ytTLURcplY,11610
sympy/benchmarks/bench_symbench.py,sha256=UMD3eYf_Poht0qxjdH2_axGwwON6cZo1Sp700Ci1M1M,2997
sympy/calculus/__init__.py,sha256=IWDc6qPbEcWyTm9QM6V8vSAs-5OtGNijimykoWz3Clc,828
sympy/calculus/__pycache__/__init__.cpython-310.pyc,,
sympy/calculus/__pycache__/accumulationbounds.cpython-310.pyc,,
sympy/calculus/__pycache__/euler.cpython-310.pyc,,
sympy/calculus/__pycache__/finite_diff.cpython-310.pyc,,
sympy/calculus/__pycache__/singularities.cpython-310.pyc,,
sympy/calculus/__pycache__/util.cpython-310.pyc,,
sympy/calculus/accumulationbounds.py,sha256=HzpumKy3ASt8Fbr4CPGNE-azxMHEvYdD6Coj1rogcys,28659
sympy/calculus/euler.py,sha256=0QrHD9TYKlSZuO8drnU3bUFJrSu8v5SncqtkRSWLjGM,3436
sympy/calculus/finite_diff.py,sha256=X7qZJ5GmHlHKokUUMFoaQqrqX2jLRq4b7W2G5aWntzM,17053
sympy/calculus/singularities.py,sha256=wBQ7WiJ1amuZStBJ-iMTiIHJexjzJHHwrc0tU2XVT10,12184
sympy/calculus/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/calculus/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/calculus/tests/__pycache__/test_accumulationbounds.cpython-310.pyc,,
sympy/calculus/tests/__pycache__/test_euler.cpython-310.pyc,,
sympy/calculus/tests/__pycache__/test_finite_diff.cpython-310.pyc,,
sympy/calculus/tests/__pycache__/test_singularities.cpython-310.pyc,,
sympy/calculus/tests/__pycache__/test_util.cpython-310.pyc,,
sympy/calculus/tests/test_accumulationbounds.py,sha256=a_Ry2nKX5WbhSe1Bk2k0W6-VWOpVTg0FnA9u8rNSIV4,11195
sympy/calculus/tests/test_euler.py,sha256=YWpts4pWSiYEwRsi5DLQ16JgC9109-9NKZIL_IO6_Aw,2683
sympy/calculus/tests/test_finite_diff.py,sha256=V52uNDNvarcK_FXnWrPZjifFMRWTy_2H4lt3FmvA4W4,7760
sympy/calculus/tests/test_singularities.py,sha256=Zj4WPkT-KlXo7TF3Ir3ug3IkS_qhnFBS7VXAMnIuCso,5272
sympy/calculus/tests/test_util.py,sha256=IJIhgudR9dym1VRAdu33G2tfSDoexNdWdz-Pgf-kh4o,18557
sympy/calculus/util.py,sha256=OxNF0-0wCBtOnuUlPLtqZ8WF0u-15Ha9FERxWBsHMRU,29048
sympy/categories/__init__.py,sha256=XiKBVC6pbDED-OVtNlSH-fGB8dB_jWLqwCEO7wBTAyA,984
sympy/categories/__pycache__/__init__.cpython-310.pyc,,
sympy/categories/__pycache__/baseclasses.cpython-310.pyc,,
sympy/categories/__pycache__/diagram_drawing.cpython-310.pyc,,
sympy/categories/baseclasses.py,sha256=1Kn7PIegQCbF78s0rhf1Bx1mbxwfQcfQi6v-QqloSoE,31360
sympy/categories/diagram_drawing.py,sha256=mhXJREdGq60SJInP6nAcBe5ZqpBXaYLEkimNcPdrKpY,95206
sympy/categories/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/categories/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/categories/tests/__pycache__/test_baseclasses.cpython-310.pyc,,
sympy/categories/tests/__pycache__/test_drawing.cpython-310.pyc,,
sympy/categories/tests/test_baseclasses.py,sha256=SwD6QsfSlrEdpD2dbkcN62CPVIRP5SadjCplLrMAoa8,5767
sympy/categories/tests/test_drawing.py,sha256=IELPpadmnQyQ2x5a5qHC8ioq5kfT1UnAl4h1vO3gbqg,27848
sympy/codegen/__init__.py,sha256=sQcJsyLyoRh9ccOPhv2eZ-wHjQrArByOON9ndj-MYgQ,974
sympy/codegen/__pycache__/__init__.cpython-310.pyc,,
sympy/codegen/__pycache__/abstract_nodes.cpython-310.pyc,,
sympy/codegen/__pycache__/algorithms.cpython-310.pyc,,
sympy/codegen/__pycache__/approximations.cpython-310.pyc,,
sympy/codegen/__pycache__/ast.cpython-310.pyc,,
sympy/codegen/__pycache__/cfunctions.cpython-310.pyc,,
sympy/codegen/__pycache__/cnodes.cpython-310.pyc,,
sympy/codegen/__pycache__/cutils.cpython-310.pyc,,
sympy/codegen/__pycache__/cxxnodes.cpython-310.pyc,,
sympy/codegen/__pycache__/fnodes.cpython-310.pyc,,
sympy/codegen/__pycache__/futils.cpython-310.pyc,,
sympy/codegen/__pycache__/matrix_nodes.cpython-310.pyc,,
sympy/codegen/__pycache__/numpy_nodes.cpython-310.pyc,,
sympy/codegen/__pycache__/pynodes.cpython-310.pyc,,
sympy/codegen/__pycache__/pyutils.cpython-310.pyc,,
sympy/codegen/__pycache__/rewriting.cpython-310.pyc,,
sympy/codegen/__pycache__/scipy_nodes.cpython-310.pyc,,
sympy/codegen/abstract_nodes.py,sha256=TY4ecftqnym5viYInnb59zGPPFXdeSGQwi--xTz6Pvo,490
sympy/codegen/algorithms.py,sha256=GEvnadOTZ3afVrbN5WE52OhCP8gzMII_AN4oHQxvEpo,6383
sympy/codegen/approximations.py,sha256=UnVbikz2vjJo8DtE02ipa6ZEsCe5lXOT_r16F5ByW4Q,6447
sympy/codegen/ast.py,sha256=rGP3ox5_0UAcMgMGThwQ1A44Sy6HJaDwx_Ma7s1GB98,56796
sympy/codegen/cfunctions.py,sha256=H8brAI1Qd-5p8_LXe9JHNpxJWU-NJdcu885fGjh2xB4,11851
sympy/codegen/cnodes.py,sha256=lsqy-JeRvr9WCk2fwDiRqPhAMFk0snInF7WlAlk9-Zg,3409
sympy/codegen/cutils.py,sha256=vlzMs8OkC5Bu4sIP-AF2mYf_tIo7Uo4r2DAI_LNhZzM,383
sympy/codegen/cxxnodes.py,sha256=Om-EBfYduFF97tgXOF68rr8zYbngem9kBRm9SJiKLSM,342
sympy/codegen/fnodes.py,sha256=P7I-TD-4H4Dr4bxFNS7p46OD9bi32l8SpFEezVWutSY,18931
sympy/codegen/futils.py,sha256=k-mxMJKr_Q_afTy6NrKNl_N2XQLBmSdZAssO5hBonNY,1792
sympy/codegen/matrix_nodes.py,sha256=0d3qXy2zaq3isyklE48lP7NP5LTF7SkLXMHMbweVGXU,2284
sympy/codegen/numpy_nodes.py,sha256=23inRIlvAF2wzaJGhi1NUg8R7NRbhtDrqICDZN909jw,3137
sympy/codegen/pynodes.py,sha256=Neo1gFQ9kC31T-gH8TeeCaDDNaDe5deIP97MRZFgMHk,243
sympy/codegen/pyutils.py,sha256=HfF6SP710Y7yExZcSesI0usVaDiWdEPEmMtyMD3JtOY,838
sympy/codegen/rewriting.py,sha256=8JtiMFgv0sA7uGu2MUU7L3uzldGw5xG1ksuk4zh2ZDE,11585
sympy/codegen/scipy_nodes.py,sha256=hYlxtGyTM0Z64Nazm1TeMZ3Y8dMsiD_HNhNvbU9eiQY,2508
sympy/codegen/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/codegen/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/codegen/tests/__pycache__/test_abstract_nodes.cpython-310.pyc,,
sympy/codegen/tests/__pycache__/test_algorithms.cpython-310.pyc,,
sympy/codegen/tests/__pycache__/test_applications.cpython-310.pyc,,
sympy/codegen/tests/__pycache__/test_approximations.cpython-310.pyc,,
sympy/codegen/tests/__pycache__/test_ast.cpython-310.pyc,,
sympy/codegen/tests/__pycache__/test_cfunctions.cpython-310.pyc,,
sympy/codegen/tests/__pycache__/test_cnodes.cpython-310.pyc,,
sympy/codegen/tests/__pycache__/test_cxxnodes.cpython-310.pyc,,
sympy/codegen/tests/__pycache__/test_fnodes.cpython-310.pyc,,
sympy/codegen/tests/__pycache__/test_matrix_nodes.cpython-310.pyc,,
sympy/codegen/tests/__pycache__/test_numpy_nodes.cpython-310.pyc,,
sympy/codegen/tests/__pycache__/test_pynodes.cpython-310.pyc,,
sympy/codegen/tests/__pycache__/test_pyutils.cpython-310.pyc,,
sympy/codegen/tests/__pycache__/test_rewriting.cpython-310.pyc,,
sympy/codegen/tests/__pycache__/test_scipy_nodes.cpython-310.pyc,,
sympy/codegen/tests/test_abstract_nodes.py,sha256=a_GKf3FpeNN8zfMc-V8AaSrQtEI1oiLfJOco2VKiSKI,451
sympy/codegen/tests/test_algorithms.py,sha256=CdOlt4SLbc64CwLLz69i3o8MqTm1xH1e5nU41k-qJls,6831
sympy/codegen/tests/test_applications.py,sha256=DWDpSsiVQy7S6pjnBSErWxDpPDRRLL8ncTMWWwaI3R4,2189
sympy/codegen/tests/test_approximations.py,sha256=SZpOUzahb_bJOceD0DLdmeiw-jN37OPmf5TRp1dyRgM,2035
sympy/codegen/tests/test_ast.py,sha256=aAWk-yAVVNAmFMkyUlYBbVA8mPlTFqULOtmXMEi3LO8,21688
sympy/codegen/tests/test_cfunctions.py,sha256=EuRwj9U00iLc2--qtY2YD7TpICndQ0gVsCXTYHrIFhQ,4613
sympy/codegen/tests/test_cnodes.py,sha256=FlI5XP39K3kC1QWKQ-QKkzNQw8TROjj5mKXJhK1UU2c,3039
sympy/codegen/tests/test_cxxnodes.py,sha256=5OwN8D_ZtKN9z5uNeUwbUkyAGzNLrTgIKUlcRWmOSpE,366
sympy/codegen/tests/test_fnodes.py,sha256=r206n8YM0D1vFP0vdjUaAR7QRpmUWw8VmqSMFxh8FU8,6643
sympy/codegen/tests/test_matrix_nodes.py,sha256=CTMSwQkW0JHaMgHR9Lys8kDk5UgQidTl4VQhWI8gw7s,1896
sympy/codegen/tests/test_numpy_nodes.py,sha256=VcG7eGVlzx9sSKRp1n9zfK0NjigxY5WOW6F_nQnnnSs,1658
sympy/codegen/tests/test_pynodes.py,sha256=Gso18KKzSwA-1AHC55SgHPAfH1GrGUCGaN6QR7iuEO0,432
sympy/codegen/tests/test_pyutils.py,sha256=yvqif7d6EpsnaBjP8XXjVo3wEENBxI6Vm01I1Wow-js,299
sympy/codegen/tests/test_rewriting.py,sha256=ELPziNI3CsJ4VS7mUbk4QWyG_94FbgZCdBKieMN20Vc,15852
sympy/codegen/tests/test_scipy_nodes.py,sha256=LBWpjTRfgWN5NLTchLZEp6m7IMtu7HbiKoztLc6KNGY,1495
sympy/combinatorics/__init__.py,sha256=Dx9xakpHuTIgy4G8zVjAY6pTu8J9_K3d_jKPizRMdVo,1500
sympy/combinatorics/__pycache__/__init__.cpython-310.pyc,,
sympy/combinatorics/__pycache__/coset_table.cpython-310.pyc,,
sympy/combinatorics/__pycache__/fp_groups.cpython-310.pyc,,
sympy/combinatorics/__pycache__/free_groups.cpython-310.pyc,,
sympy/combinatorics/__pycache__/galois.cpython-310.pyc,,
sympy/combinatorics/__pycache__/generators.cpython-310.pyc,,
sympy/combinatorics/__pycache__/graycode.cpython-310.pyc,,
sympy/combinatorics/__pycache__/group_constructs.cpython-310.pyc,,
sympy/combinatorics/__pycache__/group_numbers.cpython-310.pyc,,
sympy/combinatorics/__pycache__/homomorphisms.cpython-310.pyc,,
sympy/combinatorics/__pycache__/named_groups.cpython-310.pyc,,
sympy/combinatorics/__pycache__/partitions.cpython-310.pyc,,
sympy/combinatorics/__pycache__/pc_groups.cpython-310.pyc,,
sympy/combinatorics/__pycache__/perm_groups.cpython-310.pyc,,
sympy/combinatorics/__pycache__/permutations.cpython-310.pyc,,
sympy/combinatorics/__pycache__/polyhedron.cpython-310.pyc,,
sympy/combinatorics/__pycache__/prufer.cpython-310.pyc,,
sympy/combinatorics/__pycache__/rewritingsystem.cpython-310.pyc,,
sympy/combinatorics/__pycache__/rewritingsystem_fsm.cpython-310.pyc,,
sympy/combinatorics/__pycache__/schur_number.cpython-310.pyc,,
sympy/combinatorics/__pycache__/subsets.cpython-310.pyc,,
sympy/combinatorics/__pycache__/tensor_can.cpython-310.pyc,,
sympy/combinatorics/__pycache__/testutil.cpython-310.pyc,,
sympy/combinatorics/__pycache__/util.cpython-310.pyc,,
sympy/combinatorics/coset_table.py,sha256=A3O5l1tkFmF1mEqiab08eBcR6lAdiqKJ2uPao3Ucvlk,42935
sympy/combinatorics/fp_groups.py,sha256=EnUtSYqx4rTMhmCPpO9blxVKjwknXuXwWwQriiBaEH8,47836
sympy/combinatorics/free_groups.py,sha256=-_VxbdTlGOGp_oesIw4Umjl0pcSROXsUAl6Lzj3VOdI,39479
sympy/combinatorics/galois.py,sha256=cpd2iHaSdim5-3-cqvk58YP-AuS6CvJeCdUWpiOdIZI,17867
sympy/combinatorics/generators.py,sha256=vUIe0FgHGVFA5omJH-qHQP6NmqmnuVVV8n2RFnpTrKc,7481
sympy/combinatorics/graycode.py,sha256=xbtr8AaFYb4SMmwUi7mf7913U87jH-XEYF_3pGZfj0o,11207
sympy/combinatorics/group_constructs.py,sha256=IKx12_yWJqEQ7g-oBuAWd5VRLbCOWyL0LG4PQu43BS8,2021
sympy/combinatorics/group_numbers.py,sha256=zWfYcgzHd8n4oiosptIPQF_oUalOxTzoohT5M7pWvpo,9203
sympy/combinatorics/homomorphisms.py,sha256=dCpmPM3V2ReRuYDdXDfdMTU3pt7zkjKBkYSF6X2MfE8,18844
sympy/combinatorics/named_groups.py,sha256=zd_C9epKDrMG0drafGUcHuuJJkcMaDt1Nf2ik4NXNq8,8378
sympy/combinatorics/partitions.py,sha256=ZXqVmVNjmauhMeiTWtCCqOP38b9MJg7UlBdZa-7aICQ,20841
sympy/combinatorics/pc_groups.py,sha256=IROCLM63p4ATazWsK9qRxmx8bZjoMhWxOrTm0Q5RRpo,21351
sympy/combinatorics/perm_groups.py,sha256=htRPYCP_1IlT4CdLqkP1d3oNB67M3uTV3uwZhhwb5B8,184915
sympy/combinatorics/permutations.py,sha256=D9zxt45knRW_dR77Z36vamd0xmftPm8itnb-eyqcUGQ,87757
sympy/combinatorics/polyhedron.py,sha256=-1y5GhorUK62_gJpn4tXTLye7BcG0hAup74waDQ8y2I,35928
sympy/combinatorics/prufer.py,sha256=yN6d4w_ZVXNFhBoevA84gor4Xb5ttG529xbVgHxzKDo,12061
sympy/combinatorics/rewritingsystem.py,sha256=cT1JrAuKj9rWI3IhaHekYYt0rdG56pwFLg32pcGC9aI,17095
sympy/combinatorics/rewritingsystem_fsm.py,sha256=CKGhLqyvxY0mlmy8_Hb4WzkSdWYPUaU2yZYhz-0iZ5w,2433
sympy/combinatorics/schur_number.py,sha256=YdsyA7n_z9tyfRTSRfIjEjtnGo5EuDGBMUS09AQ2MxU,4437
sympy/combinatorics/subsets.py,sha256=oxuExuGyFnvunkmktl-vBYiLbiN66A2Q2MyzwWfy46A,16047
sympy/combinatorics/tensor_can.py,sha256=AGPgacQ2wa247u5vQNMQ4neMnCXr6qH0V_Z-0dYmHf4,40756
sympy/combinatorics/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/combinatorics/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/combinatorics/tests/__pycache__/test_coset_table.cpython-310.pyc,,
sympy/combinatorics/tests/__pycache__/test_fp_groups.cpython-310.pyc,,
sympy/combinatorics/tests/__pycache__/test_free_groups.cpython-310.pyc,,
sympy/combinatorics/tests/__pycache__/test_galois.cpython-310.pyc,,
sympy/combinatorics/tests/__pycache__/test_generators.cpython-310.pyc,,
sympy/combinatorics/tests/__pycache__/test_graycode.cpython-310.pyc,,
sympy/combinatorics/tests/__pycache__/test_group_constructs.cpython-310.pyc,,
sympy/combinatorics/tests/__pycache__/test_group_numbers.cpython-310.pyc,,
sympy/combinatorics/tests/__pycache__/test_homomorphisms.cpython-310.pyc,,
sympy/combinatorics/tests/__pycache__/test_named_groups.cpython-310.pyc,,
sympy/combinatorics/tests/__pycache__/test_partitions.cpython-310.pyc,,
sympy/combinatorics/tests/__pycache__/test_pc_groups.cpython-310.pyc,,
sympy/combinatorics/tests/__pycache__/test_perm_groups.cpython-310.pyc,,
sympy/combinatorics/tests/__pycache__/test_permutations.cpython-310.pyc,,
sympy/combinatorics/tests/__pycache__/test_polyhedron.cpython-310.pyc,,
sympy/combinatorics/tests/__pycache__/test_prufer.cpython-310.pyc,,
sympy/combinatorics/tests/__pycache__/test_rewriting.cpython-310.pyc,,
sympy/combinatorics/tests/__pycache__/test_schur_number.cpython-310.pyc,,
sympy/combinatorics/tests/__pycache__/test_subsets.cpython-310.pyc,,
sympy/combinatorics/tests/__pycache__/test_tensor_can.cpython-310.pyc,,
sympy/combinatorics/tests/__pycache__/test_testutil.cpython-310.pyc,,
sympy/combinatorics/tests/__pycache__/test_util.cpython-310.pyc,,
sympy/combinatorics/tests/test_coset_table.py,sha256=cEUF0OH6SNhN_kh069wMsq6h4eSVqbDLghrg2r9Ht48,28474
sympy/combinatorics/tests/test_fp_groups.py,sha256=C4rWnSJyk7L6uZnI-kAYvtbfFXxba0o1XjpOTkWr97s,10203
sympy/combinatorics/tests/test_free_groups.py,sha256=Xu7PkyDDlggZJbVahFz9O2x-KwxYm5MQUwEMvoL0mXw,6275
sympy/combinatorics/tests/test_galois.py,sha256=w35JRx8lmlXCdzUBNdocgATPYWBOEZ6LH-tAxOPwCQ8,2763
sympy/combinatorics/tests/test_generators.py,sha256=6YpOp0i5PRGtySPNZseQ8mjSXbwpfGfz0hDB4kfk40Q,3567
sympy/combinatorics/tests/test_graycode.py,sha256=pI4e7Y615d5Bmmxui6fdEeyca6j6KSD0YmeychV6ORk,2800
sympy/combinatorics/tests/test_group_constructs.py,sha256=jJLwMdhuUalKv4Aql9SzV2utK8Ex-IYdMecggr95pi8,450
sympy/combinatorics/tests/test_group_numbers.py,sha256=spHwsvjzEDXsdAFxkrQYOnioTgtWoVF5K7k7FbgMvfg,4149
sympy/combinatorics/tests/test_homomorphisms.py,sha256=UwBj5loCuZAiuvmqy5VAbwhCQTph8o6BzTaGrH0rzB4,3745
sympy/combinatorics/tests/test_named_groups.py,sha256=tsuDVGv4iHGEZ0BVR87_ENhyAfZvFIl0M6Dv_HX1VoY,1931
sympy/combinatorics/tests/test_partitions.py,sha256=oppszKJLLSpcEzHgespIveSmEC3fDZ0qkus1k7MBt4E,4097
sympy/combinatorics/tests/test_pc_groups.py,sha256=wfkY_ilpG0XWrhaWMVK6r7yWMeXfM8WNTyti5oE9bdk,2728
sympy/combinatorics/tests/test_perm_groups.py,sha256=bIIRYdgocLZCnGp8mtUYktDguTYlV8VMwHDAr9VUJ3A,41196
sympy/combinatorics/tests/test_permutations.py,sha256=E8J-WLCW2z9hTyTJcm1dsFgFXh8YAesIppYsXRu5pAs,20189
sympy/combinatorics/tests/test_polyhedron.py,sha256=3SWkFQKeF-p1QWP4Iu9NIA1oTxAFo1BLRrrLerBFAhw,4180
sympy/combinatorics/tests/test_prufer.py,sha256=OTJp0NxjiVswWkOuCIlnGFU2Gw4noRsrPpUJtp2XhEs,2649
sympy/combinatorics/tests/test_rewriting.py,sha256=3COHq74k6knt2rqE7hfd4ZP_6whf0Kg14tYxFmTtYrI,1787
sympy/combinatorics/tests/test_schur_number.py,sha256=wg13uTumFltWIGbVg_PEr6nhXIru19UWitsEZiakoRI,1727
sympy/combinatorics/tests/test_subsets.py,sha256=6pyhLYV5HuXvx63r-gGVHr8LSrGRXcpDudhFn9fBqX8,2635
sympy/combinatorics/tests/test_tensor_can.py,sha256=olH5D5wwTBOkZXjtqvLO6RKbvCG9KoMVK4__wDe95N4,24676
sympy/combinatorics/tests/test_testutil.py,sha256=uJlO09XgD-tImCWu1qkajiC07rK3GoN91v3_OqT5-qo,1729
sympy/combinatorics/tests/test_util.py,sha256=sOYMWHxlbM0mqalqA7jNrYMm8DKcf_GwL5YBjs96_C4,4499
sympy/combinatorics/testutil.py,sha256=GNnqy0mb6yPMa3zpGEzz2p6uxY7VtobPtwUialhfYEQ,11142
sympy/combinatorics/util.py,sha256=lkOaITBImqB9yyLvN8DU0G-vraw27cSx2XaPdAPVBhg,16296
sympy/concrete/__init__.py,sha256=2HDmg3VyLgM_ZPw3XsGpkOClGiQnyTlUNHSwVTtizA0,144
sympy/concrete/__pycache__/__init__.cpython-310.pyc,,
sympy/concrete/__pycache__/delta.cpython-310.pyc,,
sympy/concrete/__pycache__/expr_with_intlimits.cpython-310.pyc,,
sympy/concrete/__pycache__/expr_with_limits.cpython-310.pyc,,
sympy/concrete/__pycache__/gosper.cpython-310.pyc,,
sympy/concrete/__pycache__/guess.cpython-310.pyc,,
sympy/concrete/__pycache__/products.cpython-310.pyc,,
sympy/concrete/__pycache__/summations.cpython-310.pyc,,
sympy/concrete/delta.py,sha256=yqROKl2lXKqjsTVB3ZRA7t0gcQpFXC1dgPdsBp9kp7M,9939
sympy/concrete/expr_with_intlimits.py,sha256=vj4PjttB9xE5aUYu37R1A4_KtGgxcPa65jzjv8-krsc,11352
sympy/concrete/expr_with_limits.py,sha256=dL5u-b_CzwghTWIhNsGc4md8jPWfhXOvna5Lig6XVr0,21834
sympy/concrete/gosper.py,sha256=3q8gkZz_oAeBOBUfObMvwArBkBKYReHR0prVXMIqrNE,5557
sympy/concrete/guess.py,sha256=Ha12uphLNfo3AbfsGy85JsPxhbiAXJemwpz9QXRtp48,17472
sympy/concrete/products.py,sha256=s6E_Z0KuHx8MzbJzaJo2NP5aTpgIo3-oqGwgYh_osnE,18608
sympy/concrete/summations.py,sha256=o_x9C2Un0pK_codmUq5_p22QVMYa7No9M4eGOuM4XgY,55357
sympy/concrete/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/concrete/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/concrete/tests/__pycache__/test_delta.cpython-310.pyc,,
sympy/concrete/tests/__pycache__/test_gosper.cpython-310.pyc,,
sympy/concrete/tests/__pycache__/test_guess.cpython-310.pyc,,
sympy/concrete/tests/__pycache__/test_products.cpython-310.pyc,,
sympy/concrete/tests/__pycache__/test_sums_products.cpython-310.pyc,,
sympy/concrete/tests/test_delta.py,sha256=uI7xjMx7JuVb3kkN7cLR6_pGsKS4Ulq22p-Z9oti5Jc,23869
sympy/concrete/tests/test_gosper.py,sha256=ZHiZfYGCeCS9I-0oqN6sFbiYa-284GeFoGsNbhIWq4I,7987
sympy/concrete/tests/test_guess.py,sha256=TPW6Hy11Po6VLZG_dx95x3sMBYl5kcQH8wjJ6TOtu-k,3370
sympy/concrete/tests/test_products.py,sha256=caYc-xlEIrX9I_A-KPQdwp5oDprVJSbfcOaKg_qUnsM,14521
sympy/concrete/tests/test_sums_products.py,sha256=yEz0IRWvABQF39anNBnGW4Z2BcpLjz0UUI6d90m9Sl0,64452
sympy/conftest.py,sha256=wvibr2FiWTlUcLTYAFEL8tSAKWPnv_Q6Cj4lARBCM0U,2775
sympy/core/__init__.py,sha256=0L72TGngrIg2JknW3elaPSIDmkpPjWGNVfNk33wKXJ0,3123
sympy/core/__pycache__/__init__.cpython-310.pyc,,
sympy/core/__pycache__/_print_helpers.cpython-310.pyc,,
sympy/core/__pycache__/add.cpython-310.pyc,,
sympy/core/__pycache__/alphabets.cpython-310.pyc,,
sympy/core/__pycache__/assumptions.cpython-310.pyc,,
sympy/core/__pycache__/assumptions_generated.cpython-310.pyc,,
sympy/core/__pycache__/backend.cpython-310.pyc,,
sympy/core/__pycache__/basic.cpython-310.pyc,,
sympy/core/__pycache__/cache.cpython-310.pyc,,
sympy/core/__pycache__/compatibility.cpython-310.pyc,,
sympy/core/__pycache__/containers.cpython-310.pyc,,
sympy/core/__pycache__/core.cpython-310.pyc,,
sympy/core/__pycache__/coreerrors.cpython-310.pyc,,
sympy/core/__pycache__/decorators.cpython-310.pyc,,
sympy/core/__pycache__/evalf.cpython-310.pyc,,
sympy/core/__pycache__/expr.cpython-310.pyc,,
sympy/core/__pycache__/exprtools.cpython-310.pyc,,
sympy/core/__pycache__/facts.cpython-310.pyc,,
sympy/core/__pycache__/function.cpython-310.pyc,,
sympy/core/__pycache__/intfunc.cpython-310.pyc,,
sympy/core/__pycache__/kind.cpython-310.pyc,,
sympy/core/__pycache__/logic.cpython-310.pyc,,
sympy/core/__pycache__/mod.cpython-310.pyc,,
sympy/core/__pycache__/mul.cpython-310.pyc,,
sympy/core/__pycache__/multidimensional.cpython-310.pyc,,
sympy/core/__pycache__/numbers.cpython-310.pyc,,
sympy/core/__pycache__/operations.cpython-310.pyc,,
sympy/core/__pycache__/parameters.cpython-310.pyc,,
sympy/core/__pycache__/power.cpython-310.pyc,,
sympy/core/__pycache__/random.cpython-310.pyc,,
sympy/core/__pycache__/relational.cpython-310.pyc,,
sympy/core/__pycache__/rules.cpython-310.pyc,,
sympy/core/__pycache__/singleton.cpython-310.pyc,,
sympy/core/__pycache__/sorting.cpython-310.pyc,,
sympy/core/__pycache__/symbol.cpython-310.pyc,,
sympy/core/__pycache__/sympify.cpython-310.pyc,,
sympy/core/__pycache__/trace.cpython-310.pyc,,
sympy/core/__pycache__/traversal.cpython-310.pyc,,
sympy/core/_print_helpers.py,sha256=GQo9dI_BvAJtYHVFFfmroNr0L8d71UeI-tU7SGJgctk,2388
sympy/core/add.py,sha256=BNVE2e2OlBvAFNvlXo3RJz9SWXkJlr27QdgKE2gt2eM,43665
sympy/core/alphabets.py,sha256=vWBs2atOvfRK6Xfg6hc5IKiB7s_0sZIiVJpcCUJL0N4,266
sympy/core/assumptions.py,sha256=8K9rhYtT-kqS7hmc9ltkYC-wujYT3pe0BaUJNrOr8fg,23595
sympy/core/assumptions_generated.py,sha256=0TJKYIHSIFyQcVHZdIHZ19b7tqst_sY7iZwjKzcvZBM,42817
sympy/core/backend.py,sha256=xMe5flY3R_beaHiUB0kF8cHIhfA4_AM1UNmZbm6ehWc,5274
sympy/core/basic.py,sha256=NZu35-DDihLOtju09LTgKyogh9oa8vEN77c6HqA6v_s,76699
sympy/core/benchmarks/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/core/benchmarks/__pycache__/__init__.cpython-310.pyc,,
sympy/core/benchmarks/__pycache__/bench_arit.cpython-310.pyc,,
sympy/core/benchmarks/__pycache__/bench_assumptions.cpython-310.pyc,,
sympy/core/benchmarks/__pycache__/bench_basic.cpython-310.pyc,,
sympy/core/benchmarks/__pycache__/bench_expand.cpython-310.pyc,,
sympy/core/benchmarks/__pycache__/bench_numbers.cpython-310.pyc,,
sympy/core/benchmarks/__pycache__/bench_sympify.cpython-310.pyc,,
sympy/core/benchmarks/bench_arit.py,sha256=gfrnvKSXLCaUoFFxMgJhnLUp7rG9Pa_YT7OKgOrPP8E,412
sympy/core/benchmarks/bench_assumptions.py,sha256=evfZzTgOUUvvvlK0DRdDZQRqxIlGLfJYzKu8QDMxSks,177
sympy/core/benchmarks/bench_basic.py,sha256=YF0tTJ_AN_Wz11qidzM4bIhlwEhEqVc-IGVGrUx6SaA,210
sympy/core/benchmarks/bench_expand.py,sha256=xgQYQMwqgXJtKajM4JVhuL-7AW8TLY-vdBpO6uyMDoQ,427
sympy/core/benchmarks/bench_numbers.py,sha256=_1yHSbYmxriiTTzKtxFh7kJm-rXPL6roGs8MW1E5-sg,1135
sympy/core/benchmarks/bench_sympify.py,sha256=G5iGInhhbkkxSY2pS08BNG945m9m4eZlNT1aJutGt5M,138
sympy/core/cache.py,sha256=AyG7kganyV0jVx-aNBEUFogqRLHQqqFn8xU3ZSfJoaM,6172
sympy/core/compatibility.py,sha256=XQH7ezmRi6l3R23qMHN2wfA-YMRWbh2YYjPY7LRo3lo,1145
sympy/core/containers.py,sha256=e0QarkzL9olJCMpGSEnuc6fRAPp6_nQfyrzE20eXHUU,11313
sympy/core/core.py,sha256=lX3Af31Qyff-gwPiAH-vijjuOzBFSuQtN06nUqIcSTQ,547
sympy/core/coreerrors.py,sha256=OKpJwk_yE3ZMext49R-QwtTudZaXZbmTspaq1ZMMpAU,272
sympy/core/decorators.py,sha256=de6eYm3D_YdEW1rEKOIES_aEyvbjqRM98I67l8QGGVU,8217
sympy/core/evalf.py,sha256=Rby2Mo-T8HTCIRfqi0VWEIX_M8MgSEbHt11aalKG_Wo,61941
sympy/core/expr.py,sha256=Jp8UtQcbj3jBsSW2HmHUo_tROAIyorCzMD7QEBQAYrc,141927
sympy/core/exprtools.py,sha256=mCUxyyQZDSceU7eHPxV3C0mBUWI4a2Qz_LhZxJ5FXY8,51459
sympy/core/facts.py,sha256=54pFKhJwEzU8LkO7rL25TwGjIb5y5CvZleHEy_TpD68,19546
sympy/core/function.py,sha256=CqieOjJDWp86_-pD3P3QkQs5vFxbkVePNancy7JjTpI,116003
sympy/core/intfunc.py,sha256=5DzbfpYsz1eThTzZVw36-tp92qgfDZBO4axe5FzVRCU,14272
sympy/core/kind.py,sha256=9kQvtDxm-SSRGi-155XsBl_rs-oN_7dw7fNNT3mDu2Q,11540
sympy/core/logic.py,sha256=Ai2_N-pUmHngJN3usiMTNO6kfLWFVQa3WOet3VhehE8,10865
sympy/core/mod.py,sha256=V_2hEPx8do_YYZEnoPnpCe1tdAyYT4ipTZDzeXmqs7k,8434
sympy/core/mul.py,sha256=XgUtUpmsJCg-lvw-6NdQ-RkPPTCtS8fbc7akSISc41A,78544
sympy/core/multidimensional.py,sha256=NWX1okybO_nZCl9IhIOE8QYalY1WoC0zlzsvBg_E1eE,4233
sympy/core/numbers.py,sha256=l_DjxZrVtzPQxCb8jBsQWzghzqc4pN8gAIvCPt29ApY,132881
sympy/core/operations.py,sha256=1Rfzypv2TiPfZxCh4gNtexUFwpKSeMb2rzgmUyQO6EI,25082
sympy/core/parameters.py,sha256=EoT2S3W1dS2-HoV6WN7szBexXvn5_w43e2JFouKuvkU,3854
sympy/core/power.py,sha256=XrwSh6sHgSs6_I870YtOWZJI37yjEtSTiU4mys36on4,72992
sympy/core/random.py,sha256=miFdVpNKfutbkpYiIOzG9kVNUm5GTk-_nnmQqUhVDZs,6647
sympy/core/relational.py,sha256=Mw3LtSlUcMwK92X34sNwj49s3SYNT4vTFshWrc3Wi_g,51650
sympy/core/rules.py,sha256=AJuZztmYKZ_yUITLZB6rhZjDy6ROBCtajcYqPa50sjc,1496
sympy/core/singleton.py,sha256=0TrQk5Q4U-GvSXTe4Emih6B2JJg2WMu_u0pSj92wqVA,6542
sympy/core/sorting.py,sha256=PoL2-MtVeuYTu-DISGlbvqW2mt787BmWfzrwV1ibavE,10827
sympy/core/symbol.py,sha256=hacnFavMz3xJuvaLqKGgO5Byzp2sF3acXXJt91Xtfo8,29481
sympy/core/sympify.py,sha256=lBoDygXKeW0H-nHj7ufGO61QmsX-4nUK_MZbmrLBRJE,19793
sympy/core/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/core/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_args.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_arit.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_assumptions.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_basic.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_cache.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_compatibility.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_complex.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_constructor_postprocessor.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_containers.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_count_ops.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_diff.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_equal.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_eval.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_evalf.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_expand.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_expr.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_exprtools.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_facts.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_function.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_kind.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_logic.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_match.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_multidimensional.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_noncommutative.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_numbers.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_operations.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_parameters.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_power.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_priority.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_random.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_relational.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_rules.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_singleton.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_sorting.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_subs.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_symbol.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_sympify.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_traversal.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_truediv.cpython-310.pyc,,
sympy/core/tests/__pycache__/test_var.cpython-310.pyc,,
sympy/core/tests/test_args.py,sha256=JYEoCT0TH769MVnVo1BKblMXzSISI2fVLa8UupX3HE8,182401
sympy/core/tests/test_arit.py,sha256=veJWqs8HjTtguxnmqP_wwT6ENxhZSaNyrUOyFA8g3iE,78079
sympy/core/tests/test_assumptions.py,sha256=MjJdF_ymVL6mtgQx-aSr_rsNNxaTi2pHFLjyaPCBq5Q,41573
sympy/core/tests/test_basic.py,sha256=dQNZnTNG1kl4-6zLe3OsXcmxsvTi5ReWR5hpYpmEjYk,9846
sympy/core/tests/test_cache.py,sha256=p6Ci75a_T-bBXE_5HVxRKla62uSay_0Vuf57gUuH6sI,2001
sympy/core/tests/test_compatibility.py,sha256=7pvNUEGIcRrfWl3doqHlm3AdNkGlcChO69gos3Fk09A,240
sympy/core/tests/test_complex.py,sha256=koNGFMt6UMmzahJADSja_eD24gr-GG5gGCtyDgCRtPI,21906
sympy/core/tests/test_constructor_postprocessor.py,sha256=0d7vbVuKi3GCm3PKLtiNqv_Au7v6RYt1rzRdHiD08tM,2441
sympy/core/tests/test_containers.py,sha256=ijteBC6cjqzejfxXZZIELZJfMrgTQa1n6AlxHGCEQJs,7432
sympy/core/tests/test_count_ops.py,sha256=eIA2WvCuWKXVBJEGfWoJrn6WfUshX_NXttrrfyLbNnI,5665
sympy/core/tests/test_diff.py,sha256=6j4Vk9UCNRv8Oyx_4iv1ePjocwBg7_-3ftrSJ8u0cPo,5421
sympy/core/tests/test_equal.py,sha256=RoOJuu4kMe4Rkk7eNyVOJov5S1770YHiVAiziNIKd2o,1678
sympy/core/tests/test_eval.py,sha256=o0kZn3oaMidVYdNjeZYtx4uUKBoE3A2tWn2NS4hu72Q,2366
sympy/core/tests/test_evalf.py,sha256=4eapf5Wev7y0dL-1rQFZtP7o6_DlHNaa7uoU0yo45ms,28360
sympy/core/tests/test_expand.py,sha256=jbIjBEmdsPPArsxJ9206YzMS7mPUVZo7j-7alM795eU,13886
sympy/core/tests/test_expr.py,sha256=Ue1LCGTgRJ_ttypp_zY4-WF3jkOHdXq7N1lGQD2Xhg4,77579
sympy/core/tests/test_exprtools.py,sha256=L7fi319z1EeFag6pH8myqDQYQ32H193QLKMdqlxACsY,19021
sympy/core/tests/test_facts.py,sha256=YEZMZ-116VFnFqJ48h9bQsF2flhiB65trnZvJsRSh_o,11579
sympy/core/tests/test_function.py,sha256=vVoXYyGzdTO3EtlRu0sONxjB3fprXxZ7_9Ve6HdH84s,51420
sympy/core/tests/test_kind.py,sha256=NLJbwCpugzlNbaSyUlbb6NHoT_9dHuoXj023EDQMrNI,2048
sympy/core/tests/test_logic.py,sha256=_YKSIod6Q0oIz9lDs78UQQrv9LU-uKaztd7w8LWwuwY,5634
sympy/core/tests/test_match.py,sha256=2ewD4Ao9cYNvbt2TAId8oZCU0GCNWsSDx4qO5-_Xhwc,22716
sympy/core/tests/test_multidimensional.py,sha256=Fr-lagme3lwLrBpdaWP7O7oPezhIatn5X8fYYs-8bN8,848
sympy/core/tests/test_noncommutative.py,sha256=IkGPcvLO4ACVj5LMT2IUgyj68F1RBvMKbm01iqTOK04,4436
sympy/core/tests/test_numbers.py,sha256=02_Gw40sNg703HctV_rl_iW3Kep4etXASCj4T9TFD18,77165
sympy/core/tests/test_operations.py,sha256=mRxftKlrxxrn3zS3UPwqkF6Nr15l5Cv6j3c2RJX46s4,2859
sympy/core/tests/test_parameters.py,sha256=wO9D-LcMMEyf5u5-EmDwVeQ02YzYbYwtFFR_o-M4ybQ,3560
sympy/core/tests/test_power.py,sha256=Cgm5Olwi3y_qvuhOVRdVnQ0JxtQE_2bO3f1eGCUwuD4,24844
sympy/core/tests/test_priority.py,sha256=g9dGW-qT647yL4uk1D_v3M2S8rgV1Wi4JBUFyTSwUt4,3190
sympy/core/tests/test_random.py,sha256=H58NfH5BYeQ3RIscbDct6SZkHQVRJjichVUSuSrhvAU,1233
sympy/core/tests/test_relational.py,sha256=s5cUflXdGsM0lIoa2zDs3h0RHN5WAgO8f9OR3nCPo-k,43524
sympy/core/tests/test_rules.py,sha256=iwmMX7hxC_73CuX9BizeAci-cO4JDq-y1sicKBXEGA4,349
sympy/core/tests/test_singleton.py,sha256=xLJJgXwmkbKhsot_qTs-o4dniMjHUh3_va0xsA5h-KA,3036
sympy/core/tests/test_sorting.py,sha256=6BZKYqUedAR-jeHcIgsJelJHFWuougml2c1NNilxGZg,902
sympy/core/tests/test_subs.py,sha256=7ITJFDplgWBRImkcHfjRdnHqaKgjTxWb4j4WoRysvR8,30106
sympy/core/tests/test_symbol.py,sha256=zYhPWsdyQp7_NiLVthpoCB1RyP9pmJcNlTdTN2kMdfY,13043
sympy/core/tests/test_sympify.py,sha256=zz8gYacJFnf9EdK_hl9cusinkikjHSti7YaWx_LOpN8,27989
sympy/core/tests/test_traversal.py,sha256=cmgvMW8G-LZ20ZXy-wg5Vz5ogI_oq2p2bJSwMy9IMF0,4311
sympy/core/tests/test_truediv.py,sha256=RYfJX39-mNhekRE3sj5TGFZXKra4ML9vGvObsRYuD3k,854
sympy/core/tests/test_var.py,sha256=hexP-0q2nN9h_dyhKLCuvqFXgLC9e_Hroni8Ldb16Ko,1594
sympy/core/trace.py,sha256=9WC8p3OpBL6TdHmZWMDK9jaCG-16f4uZV2VptduVH98,348
sympy/core/traversal.py,sha256=C4eKiqMHijxgTq7ZEjr7v22fANP-iEXyn1mwKh0d1So,8776
sympy/crypto/__init__.py,sha256=i8GcbScXhIPbMEe7uuMgXqh_cU2mZm2f6hspIgmW5uM,2158
sympy/crypto/__pycache__/__init__.cpython-310.pyc,,
sympy/crypto/__pycache__/crypto.cpython-310.pyc,,
sympy/crypto/crypto.py,sha256=AW_4gp60x8hQxBW87nhAPjc0fh2RxSkhlj5QsHXfgjc,89667
sympy/crypto/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/crypto/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/crypto/tests/__pycache__/test_crypto.cpython-310.pyc,,
sympy/crypto/tests/test_crypto.py,sha256=gPsRBNNHwc4QDBjgT5tvWTCuC5IA5d4aZRTblxXjngk,19758
sympy/diffgeom/__init__.py,sha256=cWj4N7AfNgrYcGIBexX-UrWxfd1bP9DTNqUmLWUJ9nA,991
sympy/diffgeom/__pycache__/__init__.cpython-310.pyc,,
sympy/diffgeom/__pycache__/diffgeom.cpython-310.pyc,,
sympy/diffgeom/__pycache__/rn.cpython-310.pyc,,
sympy/diffgeom/diffgeom.py,sha256=HJ1uThmNKeADXGtIgNAyBbsdJrcN-THfKtbgXgeuU1A,72273
sympy/diffgeom/rn.py,sha256=kvgth6rNJWt94kzVospZwiH53C-s4VSiorktQNmMobQ,6264
sympy/diffgeom/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/diffgeom/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/diffgeom/tests/__pycache__/test_class_structure.cpython-310.pyc,,
sympy/diffgeom/tests/__pycache__/test_diffgeom.cpython-310.pyc,,
sympy/diffgeom/tests/__pycache__/test_function_diffgeom_book.cpython-310.pyc,,
sympy/diffgeom/tests/__pycache__/test_hyperbolic_space.cpython-310.pyc,,
sympy/diffgeom/tests/test_class_structure.py,sha256=LbRyxhhp-NnnfJ2gTn1SdlgCBQn2rhyB7xApOgcd_rM,1048
sympy/diffgeom/tests/test_diffgeom.py,sha256=3BepCr6ned-4C_3me4zScu06HXG9Qx_dBBxIpiXAvy4,14145
sympy/diffgeom/tests/test_function_diffgeom_book.py,sha256=GwhUAiAPtUv5I9oghdElMhtc6KX184tySgLSpVHhT0Q,5254
sympy/diffgeom/tests/test_hyperbolic_space.py,sha256=c4xQJ_bBS4xrMj3pfx1Ms3oC2_LwuJuNYXNZxs-cVG8,2598
sympy/discrete/__init__.py,sha256=A_Seud0IRr2gPYlz6JMQZa3sBhRL3O7gVqhIvMRRvE0,772
sympy/discrete/__pycache__/__init__.cpython-310.pyc,,
sympy/discrete/__pycache__/convolutions.cpython-310.pyc,,
sympy/discrete/__pycache__/recurrences.cpython-310.pyc,,
sympy/discrete/__pycache__/transforms.cpython-310.pyc,,
sympy/discrete/convolutions.py,sha256=9L2d2Rrn6jqGfV2lBxCV6LmcTNBZUuOIqP_fuMIzPzk,18341
sympy/discrete/recurrences.py,sha256=FqU5QG4qNNLSVBqcpL7HtKa7rQOlmHMXDQRzHZ_P_s0,5124
sympy/discrete/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/discrete/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/discrete/tests/__pycache__/test_convolutions.cpython-310.pyc,,
sympy/discrete/tests/__pycache__/test_recurrences.cpython-310.pyc,,
sympy/discrete/tests/__pycache__/test_transforms.cpython-310.pyc,,
sympy/discrete/tests/test_convolutions.py,sha256=K4S9bA1E2tg0VcFQ8SYtlxjL6HB666RxD5rriTuAK4k,17856
sympy/discrete/tests/test_recurrences.py,sha256=s5ZEZQ262gcnBLpCjJVmeKlTKQByRTQBrc-N9p_4W8c,3019
sympy/discrete/tests/test_transforms.py,sha256=vEORFaPvxmPSsw0f4Z2hLEN1wD0FdyQOYHDEY9aVm5A,5546
sympy/discrete/transforms.py,sha256=lf-n6IN881uCfTUAxPNjdUaSguiRbYW0omuR96vKNlE,11681
sympy/external/__init__.py,sha256=C6s4654Elc_X-D9UgI2cUQWiQyGDt9LG3IKUc8qqzuo,578
sympy/external/__pycache__/__init__.cpython-310.pyc,,
sympy/external/__pycache__/gmpy.cpython-310.pyc,,
sympy/external/__pycache__/importtools.cpython-310.pyc,,
sympy/external/__pycache__/ntheory.cpython-310.pyc,,
sympy/external/__pycache__/pythonmpq.cpython-310.pyc,,
sympy/external/gmpy.py,sha256=SNIEC4lGRq9ESZeihPIFH2mg2zVtAPW1_C1TsRNqnmE,9743
sympy/external/importtools.py,sha256=Q7tS2cdGZ9a4NI_1sgGuoVcSDv_rIk-Av0BpFTa6EzA,7671
sympy/external/ntheory.py,sha256=_v7Ebr10K_0-B4QY9Jt4ZEKPMxmjTlzlrs4otH5ii48,17025
sympy/external/pythonmpq.py,sha256=WOMTvHxYLXNp_vQ1F3jE_haeRlnGicbRlCTOp4ZNuo8,11243
sympy/external/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/external/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/external/tests/__pycache__/test_autowrap.cpython-310.pyc,,
sympy/external/tests/__pycache__/test_codegen.cpython-310.pyc,,
sympy/external/tests/__pycache__/test_gmpy.cpython-310.pyc,,
sympy/external/tests/__pycache__/test_importtools.cpython-310.pyc,,
sympy/external/tests/__pycache__/test_ntheory.cpython-310.pyc,,
sympy/external/tests/__pycache__/test_numpy.cpython-310.pyc,,
sympy/external/tests/__pycache__/test_pythonmpq.cpython-310.pyc,,
sympy/external/tests/__pycache__/test_scipy.cpython-310.pyc,,
sympy/external/tests/test_autowrap.py,sha256=tRDOkHdndNTmsa9sGjlZ1lFIh1rL2Awck4ec1iolb7c,9755
sympy/external/tests/test_codegen.py,sha256=zOgdevzcR5pK73FnXe3Su_2D6cuvrkP2FMqsro83G-c,12676
sympy/external/tests/test_gmpy.py,sha256=8ITpuWYeitCymWwuQLpOOVBmRb3CJsO5sbqiZcVDulE,397
sympy/external/tests/test_importtools.py,sha256=KrfontKYv11UvpazQ0vS1qyhxIvgZrCOXh1JFeACjeo,1394
sympy/external/tests/test_ntheory.py,sha256=BJWirDnX7Y7McBoXreMomGQx33YlAjiuBTYQQLhobLU,8881
sympy/external/tests/test_numpy.py,sha256=tuEji5l6GqbNjv74T6a3e8LDzI2zKUaLzvfluNXOFE0,10291
sympy/external/tests/test_pythonmpq.py,sha256=L_FdZmmk5N-VEivE_O_qZa98BZhT1WSxRfdmG817bA0,5797
sympy/external/tests/test_scipy.py,sha256=CVaw7D0-6DORgg78Q6b35SNKn05PlKwWJuqXOuU-qdY,1172
sympy/functions/__init__.py,sha256=-u5IzcQAPk9emytXfMK22EVXqpXUtSau3pQtevZYmFo,5565
sympy/functions/__pycache__/__init__.cpython-310.pyc,,
sympy/functions/combinatorial/__init__.py,sha256=WqXI3qU_TTJ7nJA8m3Z-7ZAYKoApT8f9Xs0u2bTwy_c,53
sympy/functions/combinatorial/__pycache__/__init__.cpython-310.pyc,,
sympy/functions/combinatorial/__pycache__/factorials.cpython-310.pyc,,
sympy/functions/combinatorial/__pycache__/numbers.cpython-310.pyc,,
sympy/functions/combinatorial/factorials.py,sha256=tLa_RmMcCZYwRPatvB3RYSR6GCZksA7r1zYLv2ywIlc,39184
sympy/functions/combinatorial/numbers.py,sha256=GomGSWSFBVrwdwS7ipuTpK-wxWjNDxdr_9ZlXRSj6J8,99695
sympy/functions/combinatorial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/functions/combinatorial/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/functions/combinatorial/tests/__pycache__/test_comb_factorials.cpython-310.pyc,,
sympy/functions/combinatorial/tests/__pycache__/test_comb_numbers.cpython-310.pyc,,
sympy/functions/combinatorial/tests/test_comb_factorials.py,sha256=McxI8KueWbd6Di74tsd0WlhXiPj2OfDNRsDgHPz6NyQ,26310
sympy/functions/combinatorial/tests/test_comb_numbers.py,sha256=-oqKw5NxU01Ruw9K1RY1eMFbeBxw0DBWRvAW4rQm-i8,47188
sympy/functions/elementary/__init__.py,sha256=Fj8p5qE-Rr1lqAyHI0aSgC3RYX56O-gWwo6wu-eUQYA,50
sympy/functions/elementary/__pycache__/__init__.cpython-310.pyc,,
sympy/functions/elementary/__pycache__/_trigonometric_special.cpython-310.pyc,,
sympy/functions/elementary/__pycache__/complexes.cpython-310.pyc,,
sympy/functions/elementary/__pycache__/exponential.cpython-310.pyc,,
sympy/functions/elementary/__pycache__/hyperbolic.cpython-310.pyc,,
sympy/functions/elementary/__pycache__/integers.cpython-310.pyc,,
sympy/functions/elementary/__pycache__/miscellaneous.cpython-310.pyc,,
sympy/functions/elementary/__pycache__/piecewise.cpython-310.pyc,,
sympy/functions/elementary/__pycache__/trigonometric.cpython-310.pyc,,
sympy/functions/elementary/_trigonometric_special.py,sha256=FvrgSXisxjXjyBC4-NLLya6q2YyTMNMAUqqYzuYl34g,7271
sympy/functions/elementary/benchmarks/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/functions/elementary/benchmarks/__pycache__/__init__.cpython-310.pyc,,
sympy/functions/elementary/benchmarks/__pycache__/bench_exp.cpython-310.pyc,,
sympy/functions/elementary/benchmarks/bench_exp.py,sha256=PFBYa9eMovH5XOFN5XTxWr1VDj1EBoKwn4mAtj-_DdM,185
sympy/functions/elementary/complexes.py,sha256=TmZrOEPfjKfrFe-wx_NSc-U7BwMF4Ynboy0d3Jm-sN4,43488
sympy/functions/elementary/exponential.py,sha256=LF5MB4cD_Uc3WS3-I9isekV5iripVFan-RqdPBfNQcY,42766
sympy/functions/elementary/hyperbolic.py,sha256=GdJ05Q_G2bDrtebe-E92cWePkCSjux06nJ-qL8NnJHY,69459
sympy/functions/elementary/integers.py,sha256=-6cjkzTfDay0lqGM4izXdsHyJ_Tr6daIuh0w3JPXc58,19918
sympy/functions/elementary/miscellaneous.py,sha256=uLhPf-JbpPig1jM-HJ5JN64-J69eFKOBXIrsQu9iLfc,27937
sympy/functions/elementary/piecewise.py,sha256=c2OWL2N5reSp-ztSxM7hQYBOb-oKpg7J_LG3pu9egi8,58267
sympy/functions/elementary/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/functions/elementary/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/functions/elementary/tests/__pycache__/test_complexes.cpython-310.pyc,,
sympy/functions/elementary/tests/__pycache__/test_exponential.cpython-310.pyc,,
sympy/functions/elementary/tests/__pycache__/test_hyperbolic.cpython-310.pyc,,
sympy/functions/elementary/tests/__pycache__/test_integers.cpython-310.pyc,,
sympy/functions/elementary/tests/__pycache__/test_interface.cpython-310.pyc,,
sympy/functions/elementary/tests/__pycache__/test_miscellaneous.cpython-310.pyc,,
sympy/functions/elementary/tests/__pycache__/test_piecewise.cpython-310.pyc,,
sympy/functions/elementary/tests/__pycache__/test_trigonometric.cpython-310.pyc,,
sympy/functions/elementary/tests/test_complexes.py,sha256=9dL4WtKgKnxBH7kETyPTIgzqiLY6n5spDnQd5fdK7s8,33878
sympy/functions/elementary/tests/test_exponential.py,sha256=wQ5ScHh-yG3V5Sr1Nhh9k2RpCZevZposoenMPVPrf10,29687
sympy/functions/elementary/tests/test_hyperbolic.py,sha256=85ZQi4MQVnufB6-pKc34xBqnKXhDMNSlP6xYqHg3cnc,56383
sympy/functions/elementary/tests/test_integers.py,sha256=dW380bfD9t0wJeyJJG1r3_HAIOgswS4_dLMRSHaiEBA,22315
sympy/functions/elementary/tests/test_interface.py,sha256=dBHnagyfDEXsQWlxVzWpqgCBdiJM0oUIv2QONbEYo9s,2054
sympy/functions/elementary/tests/test_miscellaneous.py,sha256=eCL30UmsusBhjvqICQNmToa1aJTML8fXav1L1J6b7FU,17148
sympy/functions/elementary/tests/test_piecewise.py,sha256=kyTYXCB5Sj2Lmhn0-2VUlG37YGGmEq7mljbooj2jUa8,62640
sympy/functions/elementary/tests/test_trigonometric.py,sha256=i3PE_27YnVzl5uzvzcC4uxB8KZDkYNJ4jXb3pB2_C5Q,89959
sympy/functions/elementary/trigonometric.py,sha256=_AqpkX40Uw86vlWVkLrb2a6LGKrD1U7m-a-NVK40BGA,115376
sympy/functions/special/__init__.py,sha256=5pjIq_RVCMsuCe1b-FlwIty30KxoUowZYKLmpIT9KHQ,59
sympy/functions/special/__pycache__/__init__.cpython-310.pyc,,
sympy/functions/special/__pycache__/bessel.cpython-310.pyc,,
sympy/functions/special/__pycache__/beta_functions.cpython-310.pyc,,
sympy/functions/special/__pycache__/bsplines.cpython-310.pyc,,
sympy/functions/special/__pycache__/delta_functions.cpython-310.pyc,,
sympy/functions/special/__pycache__/elliptic_integrals.cpython-310.pyc,,
sympy/functions/special/__pycache__/error_functions.cpython-310.pyc,,
sympy/functions/special/__pycache__/gamma_functions.cpython-310.pyc,,
sympy/functions/special/__pycache__/hyper.cpython-310.pyc,,
sympy/functions/special/__pycache__/mathieu_functions.cpython-310.pyc,,
sympy/functions/special/__pycache__/polynomials.cpython-310.pyc,,
sympy/functions/special/__pycache__/singularity_functions.cpython-310.pyc,,
sympy/functions/special/__pycache__/spherical_harmonics.cpython-310.pyc,,
sympy/functions/special/__pycache__/tensor_functions.cpython-310.pyc,,
sympy/functions/special/__pycache__/zeta_functions.cpython-310.pyc,,
sympy/functions/special/benchmarks/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/functions/special/benchmarks/__pycache__/__init__.cpython-310.pyc,,
sympy/functions/special/benchmarks/__pycache__/bench_special.cpython-310.pyc,,
sympy/functions/special/benchmarks/bench_special.py,sha256=wzAoKTccuEaG4xrEYTlYfIJuLi3kUTMTEJ9iA113Wog,164
sympy/functions/special/bessel.py,sha256=3WOVy0Cy1bz0MZ3QuU0EuFhkUoNZjnk99EHsIG0scnQ,63464
sympy/functions/special/beta_functions.py,sha256=9Znf7sJN4hd4cROD7X0gSKSvcsec_Grw5Dm75syXjNM,12750
sympy/functions/special/bsplines.py,sha256=GxW_6tXuiuWap-pc4T0v1PMcfw8FXaq3mSEf50OkLoU,10152
sympy/functions/special/delta_functions.py,sha256=NPneFMqLdwwMGZweS5C-Bok6ch1roYyO481ZNOiWp8I,19866
sympy/functions/special/elliptic_integrals.py,sha256=miwBewPJhOLksb0GhpYCZFgAIuPLOElqxIoVKH3mPIw,14892
sympy/functions/special/error_functions.py,sha256=2qA2q0aLB5eM3JqeEo-YilmiectM0BgyE5EFP0XGBsI,78778
sympy/functions/special/gamma_functions.py,sha256=OQa8Dq0-zx8-uLyqyNHOuayvZeszsffjnrUfKG0cb5U,42908
sympy/functions/special/hyper.py,sha256=lIMwPc361V1sqQnCSTfX2Ow-59zsLp5OJpj23gB0TPU,38817
sympy/functions/special/mathieu_functions.py,sha256=-3EsPJHwU1upnYz5rsc1Zy43aPpjXD1Nnmn2yA9LS6U,6606
sympy/functions/special/polynomials.py,sha256=_qDuCcXty05dTlqM_gSO-grl5-nwP4FBsfXU5td2RvU,46718
sympy/functions/special/singularity_functions.py,sha256=o8QCOnvbCsjhMn_nYC2770p4AkZZOtz_24D9VW9o618,8339
sympy/functions/special/spherical_harmonics.py,sha256=Ivwi76IeFMZhukm_TnvJYT4QEqyW2DrGF5rj4_B-dJg,10997
sympy/functions/special/tensor_functions.py,sha256=j33L8mKmgbRMjFKxEmOVjrWhudMlmgcJ-wl3csEm3-g,12282
sympy/functions/special/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/functions/special/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/functions/special/tests/__pycache__/test_bessel.cpython-310.pyc,,
sympy/functions/special/tests/__pycache__/test_beta_functions.cpython-310.pyc,,
sympy/functions/special/tests/__pycache__/test_bsplines.cpython-310.pyc,,
sympy/functions/special/tests/__pycache__/test_delta_functions.cpython-310.pyc,,
sympy/functions/special/tests/__pycache__/test_elliptic_integrals.cpython-310.pyc,,
sympy/functions/special/tests/__pycache__/test_error_functions.cpython-310.pyc,,
sympy/functions/special/tests/__pycache__/test_gamma_functions.cpython-310.pyc,,
sympy/functions/special/tests/__pycache__/test_hyper.cpython-310.pyc,,
sympy/functions/special/tests/__pycache__/test_mathieu.cpython-310.pyc,,
sympy/functions/special/tests/__pycache__/test_singularity_functions.cpython-310.pyc,,
sympy/functions/special/tests/__pycache__/test_spec_polynomials.cpython-310.pyc,,
sympy/functions/special/tests/__pycache__/test_spherical_harmonics.cpython-310.pyc,,
sympy/functions/special/tests/__pycache__/test_tensor_functions.cpython-310.pyc,,
sympy/functions/special/tests/__pycache__/test_zeta_functions.cpython-310.pyc,,
sympy/functions/special/tests/test_bessel.py,sha256=Ge59mY7EV3hzAOjzSa-XPBag12jLU_RraDPna43TZa0,34382
sympy/functions/special/tests/test_beta_functions.py,sha256=yxfgu-wmNEeMfaFABiDHYmuZpZup9FTp0ZYerlc6hhc,3786
sympy/functions/special/tests/test_bsplines.py,sha256=6UYg7IqXTi8fcSOut8TEzNVkxIA4ff-CyG22qJnbIYA,7145
sympy/functions/special/tests/test_delta_functions.py,sha256=8xhSWG4SLL86z1QKFfLk_3b--bCrxjvCaxHlODBVToE,7138
sympy/functions/special/tests/test_elliptic_integrals.py,sha256=scu7KemJ7Q2nsRcZtckQNruuthI-vla9M1sDVkLcbKM,6991
sympy/functions/special/tests/test_error_functions.py,sha256=zDN-uVoR7dDFR5mi-2KxWfJmcQ75-k-u4L2dnuIfY08,32411
sympy/functions/special/tests/test_gamma_functions.py,sha256=exHmFEtyZMJhVYTWFSBlMZhWdhQk6M2cjgNkvImD7o4,29910
sympy/functions/special/tests/test_hyper.py,sha256=SEnQ9TtyO_dSQRc94AHrbQai6Q7-tmRfDIh40gNE3FE,16694
sympy/functions/special/tests/test_mathieu.py,sha256=pqoFbnC84NDL6EQkigFtx5OQ1RFYppckTjzsm9XT0PY,1282
sympy/functions/special/tests/test_singularity_functions.py,sha256=GEXzHHeR5F0swrZzhF7pEGR0tk9Vp3Ve2c-sE4WSpsY,6249
sympy/functions/special/tests/test_spec_polynomials.py,sha256=wuiZaR_LwaM8SlNuGl3B1p4eOHC_-zZVSXMPNfzKRB4,19561
sympy/functions/special/tests/test_spherical_harmonics.py,sha256=pUFtFpNPBnJTdnqou0jniSchijyh1rdzKv8H24RT9FU,3850
sympy/functions/special/tests/test_tensor_functions.py,sha256=bblSDkPABZ6N1j1Rb2Bb5TZIzZoK1D8ks3fHizi69ZI,5546
sympy/functions/special/tests/test_zeta_functions.py,sha256=2r59_aC0QOXQsBNXqxsHPr2PkJExusI6qvSydZBPbfw,10474
sympy/functions/special/zeta_functions.py,sha256=IdshdejjEv60nNZ4gQOVG0RIgxyo22psmglxZnzwHHw,24064
sympy/galgebra.py,sha256=yEosUPSnhLp9a1NWXvpCLoU20J6TQ58XNIvw07POkVk,123
sympy/geometry/__init__.py,sha256=BU2MiKm8qJyZJ_hz1qC-3nFJTPEcuvx4hYd02jHjqSM,1240
sympy/geometry/__pycache__/__init__.cpython-310.pyc,,
sympy/geometry/__pycache__/curve.cpython-310.pyc,,
sympy/geometry/__pycache__/ellipse.cpython-310.pyc,,
sympy/geometry/__pycache__/entity.cpython-310.pyc,,
sympy/geometry/__pycache__/exceptions.cpython-310.pyc,,
sympy/geometry/__pycache__/line.cpython-310.pyc,,
sympy/geometry/__pycache__/parabola.cpython-310.pyc,,
sympy/geometry/__pycache__/plane.cpython-310.pyc,,
sympy/geometry/__pycache__/point.cpython-310.pyc,,
sympy/geometry/__pycache__/polygon.cpython-310.pyc,,
sympy/geometry/__pycache__/util.cpython-310.pyc,,
sympy/geometry/curve.py,sha256=F7b6XrlhUZ0QWLDoZJVojWfC5LeyOU-69OTFnYAREg8,10170
sympy/geometry/ellipse.py,sha256=RIxACr6GU04gfd-x-o7V8IUwz3R2VeIR4RW6HW3la4A,50263
sympy/geometry/entity.py,sha256=fvHhtSb6RvE6v-8yMyCNvm0ekLPoO7EO9J8TEsGyQGU,20668
sympy/geometry/exceptions.py,sha256=XtUMA44UTdrBWt771jegFC-TXsobhDiI-10TDH_WNFM,131
sympy/geometry/line.py,sha256=7Q6SG0Ozq7taPTitntXU0d6V2yuNbma0NBSmswTZrsM,80402
sympy/geometry/parabola.py,sha256=JalFtxCzBR8oE09agrzDtpGI9hrP4GJ-4zkg2r8Yj94,10707
sympy/geometry/plane.py,sha256=OYRw0JR7Twg5qrfxH6vX6ycOlY-tRyvSEpXphZL_wc0,26922
sympy/geometry/point.py,sha256=8DtGkhQUyleVIi5WfptZOEk2zn0kwVAZv5aeNI498tg,36652
sympy/geometry/polygon.py,sha256=aooJyJVwf6ZPuxStYgTc-2jNjVaM2YHSvpVY3XRjAuo,82027
sympy/geometry/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/geometry/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/geometry/tests/__pycache__/test_curve.cpython-310.pyc,,
sympy/geometry/tests/__pycache__/test_ellipse.cpython-310.pyc,,
sympy/geometry/tests/__pycache__/test_entity.cpython-310.pyc,,
sympy/geometry/tests/__pycache__/test_geometrysets.cpython-310.pyc,,
sympy/geometry/tests/__pycache__/test_line.cpython-310.pyc,,
sympy/geometry/tests/__pycache__/test_parabola.cpython-310.pyc,,
sympy/geometry/tests/__pycache__/test_plane.cpython-310.pyc,,
sympy/geometry/tests/__pycache__/test_point.cpython-310.pyc,,
sympy/geometry/tests/__pycache__/test_polygon.cpython-310.pyc,,
sympy/geometry/tests/__pycache__/test_util.cpython-310.pyc,,
sympy/geometry/tests/test_curve.py,sha256=xL4uRWAal4mXZxuQhcs9QOhs6MheCbFNyH1asq_a2IQ,4479
sympy/geometry/tests/test_ellipse.py,sha256=f-EorUkNl_cg63bDvm4Qrbe1PWrBaCCRg8jAmdIYBuU,26509
sympy/geometry/tests/test_entity.py,sha256=0pBKdmRIETq0pJYjxRj34B0j-o56f4iqzJy9J4buU7U,3897
sympy/geometry/tests/test_geometrysets.py,sha256=vvOWrFrJuNAFgbrVh1wPY94o-H-85FWlnIyyo2Kst9c,1911
sympy/geometry/tests/test_line.py,sha256=fLWGfHQNC3YqjxtT7zQbP--Cu0mjcPwSBfji4DA7BP0,38054
sympy/geometry/tests/test_parabola.py,sha256=kd0RU5sGOcfp6jgwgXMtvT2B6kG1-M3-iGOLnUJfZOw,6150
sympy/geometry/tests/test_plane.py,sha256=QRcfoDsJtCtcvjFb18hBEHupycLgAT2OohF6GpNShyQ,12525
sympy/geometry/tests/test_point.py,sha256=YKXQdlBTQWsIVf9l3G6iPMq0OqMTo8nZlibhJDsq_Ic,16410
sympy/geometry/tests/test_polygon.py,sha256=K--9dcz1w9IHWPH6tMHC9_-OkaIihyuCx7OQUa58pmE,27601
sympy/geometry/tests/test_util.py,sha256=sbh1QvkQG1OqvE-kt4fNNIkMWnOFi5EpaBmnZS3pzNc,7044
sympy/geometry/util.py,sha256=GEEoEaXbr200UfDLRo-u029RxwZ6wUviI0ZAabE2R8w,20687
sympy/holonomic/__init__.py,sha256=BgHIokaSOo3nwJlGO_caJHz37n6yoA8GeM9Xjn4zMpc,784
sympy/holonomic/__pycache__/__init__.cpython-310.pyc,,
sympy/holonomic/__pycache__/holonomic.cpython-310.pyc,,
sympy/holonomic/__pycache__/holonomicerrors.cpython-310.pyc,,
sympy/holonomic/__pycache__/numerical.cpython-310.pyc,,
sympy/holonomic/__pycache__/recurrence.cpython-310.pyc,,
sympy/holonomic/holonomic.py,sha256=N8CBf-HqC5fZT5JJAzXEjInpvkgTGc1VGgq-lQZTZGc,92513
sympy/holonomic/holonomicerrors.py,sha256=qDyUoGbrRjPtVax4SeEEf_o6-264mASEZO_rZETXH5o,1193
sympy/holonomic/numerical.py,sha256=rLS8zYc4Ir2iCM-xOk4n3QPu18hSLFLAQT9Fs6B3-pA,2736
sympy/holonomic/recurrence.py,sha256=yhDqoRnIndaHjWFN4MHSG44ZYQARtZsihusDoQnwqHI,10929
sympy/holonomic/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/holonomic/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/holonomic/tests/__pycache__/test_holonomic.cpython-310.pyc,,
sympy/holonomic/tests/__pycache__/test_recurrence.cpython-310.pyc,,
sympy/holonomic/tests/test_holonomic.py,sha256=NuoJtJi3RlpMYhMU_bGH1tuFuFoOZZrrSdMpgdHGhf0,35333
sympy/holonomic/tests/test_recurrence.py,sha256=qHv0kn1Q4-aCD7XmbDK2xIdkjF0XkeZUKD2yeLajiq0,1342
sympy/integrals/__init__.py,sha256=pZ-C3tDP_8woKActNoS7IwyW-9AuB5PMHB5B0ohlpw8,1970
sympy/integrals/__pycache__/__init__.cpython-310.pyc,,
sympy/integrals/__pycache__/deltafunctions.cpython-310.pyc,,
sympy/integrals/__pycache__/heurisch.cpython-310.pyc,,
sympy/integrals/__pycache__/integrals.cpython-310.pyc,,
sympy/integrals/__pycache__/intpoly.cpython-310.pyc,,
sympy/integrals/__pycache__/laplace.cpython-310.pyc,,
sympy/integrals/__pycache__/manualintegrate.cpython-310.pyc,,
sympy/integrals/__pycache__/meijerint.cpython-310.pyc,,
sympy/integrals/__pycache__/meijerint_doc.cpython-310.pyc,,
sympy/integrals/__pycache__/prde.cpython-310.pyc,,
sympy/integrals/__pycache__/quadrature.cpython-310.pyc,,
sympy/integrals/__pycache__/rationaltools.cpython-310.pyc,,
sympy/integrals/__pycache__/rde.cpython-310.pyc,,
sympy/integrals/__pycache__/risch.cpython-310.pyc,,
sympy/integrals/__pycache__/singularityfunctions.cpython-310.pyc,,
sympy/integrals/__pycache__/transforms.cpython-310.pyc,,
sympy/integrals/__pycache__/trigonometry.cpython-310.pyc,,
sympy/integrals/benchmarks/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/integrals/benchmarks/__pycache__/__init__.cpython-310.pyc,,
sympy/integrals/benchmarks/__pycache__/bench_integrate.cpython-310.pyc,,
sympy/integrals/benchmarks/__pycache__/bench_trigintegrate.cpython-310.pyc,,
sympy/integrals/benchmarks/bench_integrate.py,sha256=vk6wAO1bqzFT9oW4qsW7nKGfc_gP0XaB5PMYKx5339Q,396
sympy/integrals/benchmarks/bench_trigintegrate.py,sha256=8XU3uB3mcavigvzHQZA7H1sHI32zgT-9RkSnLa-Y3Vc,305
sympy/integrals/deltafunctions.py,sha256=ysIQLdRBcG_YR-bVDoxt-sxEVU8TG77oSgM-J0gI0mE,7435
sympy/integrals/heurisch.py,sha256=Huq3dBJEXvYGzkBboXnG6xFCGorc5Uftf5ssbiltyIs,26706
sympy/integrals/integrals.py,sha256=iQC0-E526TL3c5SVON4uNhK6Oi0vdColIGKDngIoBiw,64868
sympy/integrals/intpoly.py,sha256=SXjd_f295YrYsvoQpzE2EQM5xaQnnj0zvHdYW5KEdn0,43266
sympy/integrals/laplace.py,sha256=y6sj9ll2y5k5FEUPIELLxI_eEuX5DHdkgHIssm1olOc,86745
sympy/integrals/manualintegrate.py,sha256=DlRgEYurrERhvkIph6MnXblixI04R-qQjT73nD6oeWM,75672
sympy/integrals/meijerint.py,sha256=Lvwf71LTQExh-9VeHWlqLCm-ubZ3xYWpdD04V8FM4P0,80794
sympy/integrals/meijerint_doc.py,sha256=mGlIu2CLmOulSGiN7n7kQ9w2DTcQfExJPaf-ee6HXlY,1165
sympy/integrals/prde.py,sha256=x4F91R-MQPeU1szYHKu0b9lz0gtuASeAus6utfYSbVc,52047
sympy/integrals/quadrature.py,sha256=6Bg3JmlIjIduIfaGfNVcwNfSrgEiLOszcN8WPzsXNqE,17064
sympy/integrals/rationaltools.py,sha256=GfARb5VJLHtD_uT0qwa6N1QXnR39CEO5EuAqp3Vf0iQ,11400
sympy/integrals/rde.py,sha256=YJ8x9uGufZzKotbaohwPb7ZXxEwSLlkAuyKo-XM7r7w,27390
sympy/integrals/risch.py,sha256=fXVWKL5zFajw9Q0AvsOprmFLK5NhEau7djHOSyQzp_w,67353
sympy/integrals/singularityfunctions.py,sha256=ONI8x-ed-IcqOF4K2l0LVUvEUN2_dHztvL4auRsi67U,2235
sympy/integrals/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/integrals/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/integrals/tests/__pycache__/test_deltafunctions.cpython-310.pyc,,
sympy/integrals/tests/__pycache__/test_failing_integrals.cpython-310.pyc,,
sympy/integrals/tests/__pycache__/test_heurisch.cpython-310.pyc,,
sympy/integrals/tests/__pycache__/test_integrals.cpython-310.pyc,,
sympy/integrals/tests/__pycache__/test_intpoly.cpython-310.pyc,,
sympy/integrals/tests/__pycache__/test_laplace.cpython-310.pyc,,
sympy/integrals/tests/__pycache__/test_lineintegrals.cpython-310.pyc,,
sympy/integrals/tests/__pycache__/test_manual.cpython-310.pyc,,
sympy/integrals/tests/__pycache__/test_meijerint.cpython-310.pyc,,
sympy/integrals/tests/__pycache__/test_prde.cpython-310.pyc,,
sympy/integrals/tests/__pycache__/test_quadrature.cpython-310.pyc,,
sympy/integrals/tests/__pycache__/test_rationaltools.cpython-310.pyc,,
sympy/integrals/tests/__pycache__/test_rde.cpython-310.pyc,,
sympy/integrals/tests/__pycache__/test_risch.cpython-310.pyc,,
sympy/integrals/tests/__pycache__/test_singularityfunctions.cpython-310.pyc,,
sympy/integrals/tests/__pycache__/test_transforms.cpython-310.pyc,,
sympy/integrals/tests/__pycache__/test_trigonometry.cpython-310.pyc,,
sympy/integrals/tests/test_deltafunctions.py,sha256=ivFjS-WlLQ4aMqjVS7ZzMChP2Mmw_JUPnwI9otiLnvs,3709
sympy/integrals/tests/test_failing_integrals.py,sha256=HKMA6O26exCIRWQ43KyKugOvJL3gOei3r9JObcKo8p0,7438
sympy/integrals/tests/test_heurisch.py,sha256=k2f509V0gG06dgquyWEueHORNOCh3FX7vykr8hLDUOs,14401
sympy/integrals/tests/test_integrals.py,sha256=2EO8ZFU-Ytc7i3y3lKz04DVZAtkaAcCsX7oOoD8_z7I,79599
sympy/integrals/tests/test_intpoly.py,sha256=NzGhkR2pUMfd8lIU2cFR9bFa0J89RzpHs3zDggAWtXo,37445
sympy/integrals/tests/test_laplace.py,sha256=9JiZozNVQU-mpjOGBkPPTaLYPYsdWl6Uhu0u1ZxtB8s,37834
sympy/integrals/tests/test_lineintegrals.py,sha256=zcPJ2n7DYt9KsgAe38t0gq3ARApUlb-kBahLThuRcq8,450
sympy/integrals/tests/test_manual.py,sha256=unQ6Ew81SCHvVMva_aAOndh7hWpLeDwBtXs6W-yC0jE,34551
sympy/integrals/tests/test_meijerint.py,sha256=G22dppQGMFU3JGexHZs65nce8UAK9wTilZn0D1XvZu0,32594
sympy/integrals/tests/test_prde.py,sha256=2BZmEDasdx_3l64-9hioArysDj6Nl520GpQN2xnEE_A,16360
sympy/integrals/tests/test_quadrature.py,sha256=iFMdqck36gkL-yksLflawIOYmw-0PzO2tFj_qdK6Hjg,19919
sympy/integrals/tests/test_rationaltools.py,sha256=7QiPnpBXl7lo32RmhXo7ED6FYj5I1gjEFOziJYlqPtI,5669
sympy/integrals/tests/test_rde.py,sha256=4d3vJupa-hRN4yNDISY8IC3rSI_cZW5BbtxoZm14y-Y,9571
sympy/integrals/tests/test_risch.py,sha256=HaWg0JnErdrNzNmVfyz2Zz4XAgZPVVpZPt6Map3sQ58,38630
sympy/integrals/tests/test_singularityfunctions.py,sha256=CSrHie59_NjNZ9B2GaHzKPNsMzxm5Kh6GuxlYk8zTuI,1266
sympy/integrals/tests/test_transforms.py,sha256=Of9XEpzwB0CGy722z41oOdUEbfmAscsAhMute2_8oeA,27077
sympy/integrals/tests/test_trigonometry.py,sha256=moMYr_Prc7gaYPjBK0McLjRpTEes2veUlN0vGv9UyEA,3869
sympy/integrals/transforms.py,sha256=Kvujaxl4MUPzgQKpkpHGflyVk38s3n9NBfelnEYBjrc,51748
sympy/integrals/trigonometry.py,sha256=iOoBDGFDZx8PNbgL3XeZEd80I8ro0WAizNuC4P-u8x0,11083
sympy/interactive/__init__.py,sha256=yokwEO2HF3eN2Xu65JSpUUsN4iYmPvvU4m_64f3Q33o,251
sympy/interactive/__pycache__/__init__.cpython-310.pyc,,
sympy/interactive/__pycache__/printing.cpython-310.pyc,,
sympy/interactive/__pycache__/session.cpython-310.pyc,,
sympy/interactive/__pycache__/traversal.cpython-310.pyc,,
sympy/interactive/printing.py,sha256=l-IMWAtA7bpvaVNmnI4-r5GvTtmi00dnzh1KEMB7lGs,22694
sympy/interactive/session.py,sha256=sG546e0mAtT0OrFkYNVM7QGvkWrDhAQZ5E1hfx03iBQ,15329
sympy/interactive/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/interactive/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/interactive/tests/__pycache__/test_interactive.cpython-310.pyc,,
sympy/interactive/tests/__pycache__/test_ipython.cpython-310.pyc,,
sympy/interactive/tests/test_interactive.py,sha256=Pbopy9lODrd_P46_xxlWxLwqPfG6_4J3CWWC4IqfDL4,485
sympy/interactive/tests/test_ipython.py,sha256=iYNmuETjveHBVpOywyv_jStQWkFwf1GuEBjoZUVhxK4,11799
sympy/interactive/traversal.py,sha256=XbccdO6msNAvrG6FFJl2n4XmIiRISnvda4QflfEPg7U,3189
sympy/liealgebras/__init__.py,sha256=K8tw7JqG33_y6mYl1LTr8ZNtKH5L21BqkjCHfLhP4aA,79
sympy/liealgebras/__pycache__/__init__.cpython-310.pyc,,
sympy/liealgebras/__pycache__/cartan_matrix.cpython-310.pyc,,
sympy/liealgebras/__pycache__/cartan_type.cpython-310.pyc,,
sympy/liealgebras/__pycache__/dynkin_diagram.cpython-310.pyc,,
sympy/liealgebras/__pycache__/root_system.cpython-310.pyc,,
sympy/liealgebras/__pycache__/type_a.cpython-310.pyc,,
sympy/liealgebras/__pycache__/type_b.cpython-310.pyc,,
sympy/liealgebras/__pycache__/type_c.cpython-310.pyc,,
sympy/liealgebras/__pycache__/type_d.cpython-310.pyc,,
sympy/liealgebras/__pycache__/type_e.cpython-310.pyc,,
sympy/liealgebras/__pycache__/type_f.cpython-310.pyc,,
sympy/liealgebras/__pycache__/type_g.cpython-310.pyc,,
sympy/liealgebras/__pycache__/weyl_group.cpython-310.pyc,,
sympy/liealgebras/cartan_matrix.py,sha256=yr2LoZi_Gxmu-EMKgFuPOPNMYPOsxucLAS6oRpSYi2U,524
sympy/liealgebras/cartan_type.py,sha256=xLklg8Y5s40je6sXwmLmG9iyYi9YEk9KoxTSFz1GtdI,1790
sympy/liealgebras/dynkin_diagram.py,sha256=ZzGuBGNOJ3lPDdJDs4n8hvGbz6wLhC5mwb8zFkDmyPw,535
sympy/liealgebras/root_system.py,sha256=GwWc4iploE7ogS9LTOkkjsij1mbPMQxbV2_pvNriYbE,6727
sympy/liealgebras/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/liealgebras/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/liealgebras/tests/__pycache__/test_cartan_matrix.cpython-310.pyc,,
sympy/liealgebras/tests/__pycache__/test_cartan_type.cpython-310.pyc,,
sympy/liealgebras/tests/__pycache__/test_dynkin_diagram.cpython-310.pyc,,
sympy/liealgebras/tests/__pycache__/test_root_system.cpython-310.pyc,,
sympy/liealgebras/tests/__pycache__/test_type_A.cpython-310.pyc,,
sympy/liealgebras/tests/__pycache__/test_type_B.cpython-310.pyc,,
sympy/liealgebras/tests/__pycache__/test_type_C.cpython-310.pyc,,
sympy/liealgebras/tests/__pycache__/test_type_D.cpython-310.pyc,,
sympy/liealgebras/tests/__pycache__/test_type_E.cpython-310.pyc,,
sympy/liealgebras/tests/__pycache__/test_type_F.cpython-310.pyc,,
sympy/liealgebras/tests/__pycache__/test_type_G.cpython-310.pyc,,
sympy/liealgebras/tests/__pycache__/test_weyl_group.cpython-310.pyc,,
sympy/liealgebras/tests/test_cartan_matrix.py,sha256=KCsakn0fHKHRbIUcrUkHBIKkudl3_ISUdHrfJy-UOd4,303
sympy/liealgebras/tests/test_cartan_type.py,sha256=t5PvYYDXbNIFL3CV59Je7SBIAeLLf-W3mOINPUoHK6E,339
sympy/liealgebras/tests/test_dynkin_diagram.py,sha256=DSixbnt_yd0zrhKzXW_XqkXWXYe1Dk2MmXN-Rjb1dGg,260
sympy/liealgebras/tests/test_root_system.py,sha256=YmGBdUeJ4PkLSfAfRgTF7GW62RCEd5nH27FSX9UaG5Q,927
sympy/liealgebras/tests/test_type_A.py,sha256=x7QmpjxsGmXol-IYVtN1lmIOmM3HLYwpX1tSG5h6FMM,657
sympy/liealgebras/tests/test_type_B.py,sha256=Gw0GP24wP2rPn38Wwla9W7BwWH4JtCGpaprZb5W6JVY,642
sympy/liealgebras/tests/test_type_C.py,sha256=ysSy-vzE9lNwzAunrmvnFkLBoJwF7W2On7QpqS6RI1s,927
sympy/liealgebras/tests/test_type_D.py,sha256=qrO4oCjrjkp1uDvrNtbgANVyaOExqOLNtIpIxD1uH0U,764
sympy/liealgebras/tests/test_type_E.py,sha256=suG6DaZ2R74ovnJrY6GGyiu9A6FjUkouRNUFPnEczqk,775
sympy/liealgebras/tests/test_type_F.py,sha256=yUQJ7LzTemv4Cd1XW_dr3x7KEI07BahsWAyJfXLS1eA,1378
sympy/liealgebras/tests/test_type_G.py,sha256=wVa6qcAHbdrc9dA63samexHL35cWWJS606pom-6mH2Q,548
sympy/liealgebras/tests/test_weyl_group.py,sha256=HrzojRECbhNUsdLFQAXYnJEt8LfktOSJZuqVE45aRnc,1501
sympy/liealgebras/type_a.py,sha256=l5SUJknj1xLgwRVMuOsVmwbcxY2V6PU59jBtssylKH4,4314
sympy/liealgebras/type_b.py,sha256=50xdcrec1nFFtyUWOmP2Qm9ZW1zpbrgwbz_YPKp55Go,4563
sympy/liealgebras/type_c.py,sha256=bXGqPiLN3x4NAsM-ZHKJPxFO6RY7lDZUckCarIODEi0,4439
sympy/liealgebras/type_d.py,sha256=Rgh7KpI5FQnDai6KVfoz_TREYaKxqvINDXu6Zdu-7EQ,4694
sympy/liealgebras/type_e.py,sha256=Uf-QzI-6bRJeI91stGHsiesknwBEVYIjZaiNP-2bIiY,9780
sympy/liealgebras/type_f.py,sha256=boKDhOxRcAWDBHsEYk4j14vUvT0mO3UkRq6QzqoPOes,4417
sympy/liealgebras/type_g.py,sha256=Ife98dGPtarGd-ii8hJbXdB0SMsct4okDkSX2wLN8XI,2965
sympy/liealgebras/weyl_group.py,sha256=5YFA8qC4GWDM0WLNR_6VgpuNFZDfyDA7fBFjBcZaLgA,14557
sympy/logic/__init__.py,sha256=RfoXrq9MESnXdL7PkwpYEfWeaxH6wBPHiE4zCgLKvk0,456
sympy/logic/__pycache__/__init__.cpython-310.pyc,,
sympy/logic/__pycache__/boolalg.cpython-310.pyc,,
sympy/logic/__pycache__/inference.cpython-310.pyc,,
sympy/logic/algorithms/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/logic/algorithms/__pycache__/__init__.cpython-310.pyc,,
sympy/logic/algorithms/__pycache__/dpll.cpython-310.pyc,,
sympy/logic/algorithms/__pycache__/dpll2.cpython-310.pyc,,
sympy/logic/algorithms/__pycache__/lra_theory.cpython-310.pyc,,
sympy/logic/algorithms/__pycache__/minisat22_wrapper.cpython-310.pyc,,
sympy/logic/algorithms/__pycache__/pycosat_wrapper.cpython-310.pyc,,
sympy/logic/algorithms/__pycache__/z3_wrapper.cpython-310.pyc,,
sympy/logic/algorithms/dpll.py,sha256=zqiZDm1oD5sNxFqm_0Hen6NjfILIDp5uRgEOad1vYXI,9188
sympy/logic/algorithms/dpll2.py,sha256=ENfSXXAooWW4SaYpzTiViaKXSvF3tha2tHubQyLouGw,21261
sympy/logic/algorithms/lra_theory.py,sha256=z6Cv07NB3jh-dD0uxpsLdI10NBF3iLVmXuYilQiPTjE,31817
sympy/logic/algorithms/minisat22_wrapper.py,sha256=uINcvkIHGWYJb8u-Q0OgnSgaHfVUd9tYYFbBAVNiASo,1317
sympy/logic/algorithms/pycosat_wrapper.py,sha256=0vNFTbu9-YhSfjwYTsZsP_Z4HM8WpL11-xujLBS1kYg,1207
sympy/logic/algorithms/z3_wrapper.py,sha256=mFmf7DWDV0Zu7006EdFK4qEDTP7sfmcXrfGZkMK97vo,3747
sympy/logic/boolalg.py,sha256=W15ac_8wL3diM0a_MWgyx8MBain-WcgBZTMmi7BlBCM,113335
sympy/logic/inference.py,sha256=J2D8t9iHCSdotzS9iq6g3EvLPsI2B10kiNbmHsIz_oY,8983
sympy/logic/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/logic/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/logic/tests/__pycache__/test_boolalg.cpython-310.pyc,,
sympy/logic/tests/__pycache__/test_dimacs.cpython-310.pyc,,
sympy/logic/tests/__pycache__/test_inference.cpython-310.pyc,,
sympy/logic/tests/__pycache__/test_lra_theory.cpython-310.pyc,,
sympy/logic/tests/test_boolalg.py,sha256=7wlezYyLQwRyaC_KIA77bhFvXMVswKmlrMBzaIBYEy0,49058
sympy/logic/tests/test_dimacs.py,sha256=EK_mA_k9zBLcQLTOKTZVrGhnGuQNza5mwXDQD_f-X1c,3886
sympy/logic/tests/test_inference.py,sha256=-PhFW4IYP_cf_4t5qYJH0bRzPiwokLZmUDrGixtVIU4,15318
sympy/logic/tests/test_lra_theory.py,sha256=hNCH66hP_32x5ioyqM6ltd0DbEl72kdctCBu-H6egG0,16834
sympy/logic/utilities/__init__.py,sha256=WTn2vBgHcmhONRWI79PdMYNk8UxYDzsxRlZWuc-wtNI,55
sympy/logic/utilities/__pycache__/__init__.cpython-310.pyc,,
sympy/logic/utilities/__pycache__/dimacs.cpython-310.pyc,,
sympy/logic/utilities/dimacs.py,sha256=aaHdXUOD8kZHWbTzuZc6c5xMM8O1oHbRxyOxPpVMMdQ,1663
sympy/matrices/__init__.py,sha256=i2a37WlcCj8-AKG_Yy8BBdOHFgAuWois_2IcD_Ih00s,2634
sympy/matrices/__pycache__/__init__.cpython-310.pyc,,
sympy/matrices/__pycache__/common.cpython-310.pyc,,
sympy/matrices/__pycache__/decompositions.cpython-310.pyc,,
sympy/matrices/__pycache__/dense.cpython-310.pyc,,
sympy/matrices/__pycache__/determinant.cpython-310.pyc,,
sympy/matrices/__pycache__/eigen.cpython-310.pyc,,
sympy/matrices/__pycache__/exceptions.cpython-310.pyc,,
sympy/matrices/__pycache__/graph.cpython-310.pyc,,
sympy/matrices/__pycache__/immutable.cpython-310.pyc,,
sympy/matrices/__pycache__/inverse.cpython-310.pyc,,
sympy/matrices/__pycache__/kind.cpython-310.pyc,,
sympy/matrices/__pycache__/matrices.cpython-310.pyc,,
sympy/matrices/__pycache__/matrixbase.cpython-310.pyc,,
sympy/matrices/__pycache__/normalforms.cpython-310.pyc,,
sympy/matrices/__pycache__/reductions.cpython-310.pyc,,
sympy/matrices/__pycache__/repmatrix.cpython-310.pyc,,
sympy/matrices/__pycache__/solvers.cpython-310.pyc,,
sympy/matrices/__pycache__/sparse.cpython-310.pyc,,
sympy/matrices/__pycache__/sparsetools.cpython-310.pyc,,
sympy/matrices/__pycache__/subspaces.cpython-310.pyc,,
sympy/matrices/__pycache__/utilities.cpython-310.pyc,,
sympy/matrices/benchmarks/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/matrices/benchmarks/__pycache__/__init__.cpython-310.pyc,,
sympy/matrices/benchmarks/__pycache__/bench_matrix.cpython-310.pyc,,
sympy/matrices/benchmarks/bench_matrix.py,sha256=vGMlg-2il2cFeAWrf0NJ6pzPX3Yd3ZQMxFgQ4q5ILQE,306
sympy/matrices/common.py,sha256=w8PtQ5HSn6Xwxz7yIVHOqYYFfk9n-XF7dQ7PcZZOkQI,95530
sympy/matrices/decompositions.py,sha256=5OjwFe5Z_eM2XoZPqrNFY9-Esq3cO_8yqFxT9QBPHgQ,47865
sympy/matrices/dense.py,sha256=m0gmcHGsX4jyci7YJDy_5yyG2PJEU4MdzZ4DrMz_6uA,30434
sympy/matrices/determinant.py,sha256=I3qn3HFSLl4_oWHSfzdkTd424qUE55h-xRz5Ikm_DcI,34551
sympy/matrices/eigen.py,sha256=v09mqb2hZ0HqGbGzYpELOatIHnGTP5XADE-1RGcua-g,39811
sympy/matrices/exceptions.py,sha256=8diN_ojMGC93XpqvZeS5ow4crlFxSkm6WqtnIK5j81E,503
sympy/matrices/expressions/__init__.py,sha256=IMqXCSsPh0Vp_MC9HZTudA5DGM4WBq_yB-Bst0azyM8,1692
sympy/matrices/expressions/__pycache__/__init__.cpython-310.pyc,,
sympy/matrices/expressions/__pycache__/_shape.cpython-310.pyc,,
sympy/matrices/expressions/__pycache__/adjoint.cpython-310.pyc,,
sympy/matrices/expressions/__pycache__/applyfunc.cpython-310.pyc,,
sympy/matrices/expressions/__pycache__/blockmatrix.cpython-310.pyc,,
sympy/matrices/expressions/__pycache__/companion.cpython-310.pyc,,
sympy/matrices/expressions/__pycache__/determinant.cpython-310.pyc,,
sympy/matrices/expressions/__pycache__/diagonal.cpython-310.pyc,,
sympy/matrices/expressions/__pycache__/dotproduct.cpython-310.pyc,,
sympy/matrices/expressions/__pycache__/factorizations.cpython-310.pyc,,
sympy/matrices/expressions/__pycache__/fourier.cpython-310.pyc,,
sympy/matrices/expressions/__pycache__/funcmatrix.cpython-310.pyc,,
sympy/matrices/expressions/__pycache__/hadamard.cpython-310.pyc,,
sympy/matrices/expressions/__pycache__/inverse.cpython-310.pyc,,
sympy/matrices/expressions/__pycache__/kronecker.cpython-310.pyc,,
sympy/matrices/expressions/__pycache__/matadd.cpython-310.pyc,,
sympy/matrices/expressions/__pycache__/matexpr.cpython-310.pyc,,
sympy/matrices/expressions/__pycache__/matmul.cpython-310.pyc,,
sympy/matrices/expressions/__pycache__/matpow.cpython-310.pyc,,
sympy/matrices/expressions/__pycache__/permutation.cpython-310.pyc,,
sympy/matrices/expressions/__pycache__/sets.cpython-310.pyc,,
sympy/matrices/expressions/__pycache__/slice.cpython-310.pyc,,
sympy/matrices/expressions/__pycache__/special.cpython-310.pyc,,
sympy/matrices/expressions/__pycache__/trace.cpython-310.pyc,,
sympy/matrices/expressions/__pycache__/transpose.cpython-310.pyc,,
sympy/matrices/expressions/_shape.py,sha256=TyQSwGx41aaMyAYs5Q7Er6atKVAdWK7DJ6YIVsiEAZg,3062
sympy/matrices/expressions/adjoint.py,sha256=HV4OIWgmS2f_1_4PHRhQpgNmKJVCoX0eyHmrM6gfN5g,1515
sympy/matrices/expressions/applyfunc.py,sha256=8scpWjZp7yzuvUNr0mxN03KgyTzLRa5NkGl9s81YPgY,6751
sympy/matrices/expressions/blockmatrix.py,sha256=od7rlvo554XYYpEapUZ9LI6ycO7mH-rdgwmqW7wzJQc,31855
sympy/matrices/expressions/companion.py,sha256=lXUJRbjQR6e1mdHQdJwNIJXMW80XmKbOVqNvUXjB57U,1705
sympy/matrices/expressions/determinant.py,sha256=RyQXgUgqJkv_rvUPrn1_rOY45wzp5zYH6ZOf9S8NK8s,3281
sympy/matrices/expressions/diagonal.py,sha256=XHWoT-Jv5QwJVsGNbfxHnNG2sygPy1CeR_t6zr8oUoM,6328
sympy/matrices/expressions/dotproduct.py,sha256=sKdUhwVKTB3LEvd8xMwCDexNoQ1Dz43DCYsmm3UwFWw,1911
sympy/matrices/expressions/factorizations.py,sha256=zFNjMBsJqhsIcDD8Me4W8-Q-TV89WptfG3Dd9yK_tPE,1456
sympy/matrices/expressions/fourier.py,sha256=dvaftgB9jgkR_8ETyhzyVLtf1ZJu_wQC-ZbpTYMXZGE,2094
sympy/matrices/expressions/funcmatrix.py,sha256=q6R75wLn0UdV4xJdVJUrNaofV1k1egXLLQdBeZcPtiY,3520
sympy/matrices/expressions/hadamard.py,sha256=feXSZy0vbqxTzg0JeLmkSegiF4T2v5dOdcv0UQczK38,13920
sympy/matrices/expressions/inverse.py,sha256=4UwgHWSIHgEoKOniObkClMYN9DrO2xNyvOSVToXSpj8,2963
sympy/matrices/expressions/kronecker.py,sha256=kzCHqXDtcZGVQPln521lfN5redNwj6IjXJwjbv_Dkhg,13404
sympy/matrices/expressions/matadd.py,sha256=0MSanal1HKVEuCBEpehKwfUX4fuM9UMy6Fg2H5noA0s,4773
sympy/matrices/expressions/matexpr.py,sha256=cKhobdpLWWsc8Tlcf-om5MEsmbcMOLsF9zIiKE0bik8,27548
sympy/matrices/expressions/matmul.py,sha256=WT98yBmRPbtbqZOVbha2-gD5zcGXrr3dps5unNk3_uw,15510
sympy/matrices/expressions/matpow.py,sha256=puuYB49wr1WzFxhT9DcTWtF2bGDFWcbJ7oh14ATQARs,5140
sympy/matrices/expressions/permutation.py,sha256=Xe7yOx-EgeD6JrqWc4L-ApdN-3ZiV8XS_LQPmc1lhGw,8050
sympy/matrices/expressions/sets.py,sha256=3zase_rDn2QdaXETX78BgkfKiWcRC7FmwVjjIU-WmdY,2033
sympy/matrices/expressions/slice.py,sha256=aNdY1Ey4VJR-UCvoORX2kh2DmA6QjOp-waENvWg8WVE,3355
sympy/matrices/expressions/special.py,sha256=hywkygOQjcJEGQn2fG4dF8oUqXL3N42U1TxbdJr4-6E,7499
sympy/matrices/expressions/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/matrices/expressions/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_adjoint.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_applyfunc.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_blockmatrix.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_companion.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_derivatives.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_determinant.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_diagonal.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_dotproduct.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_factorizations.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_fourier.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_funcmatrix.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_hadamard.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_indexing.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_inverse.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_kronecker.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_matadd.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_matexpr.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_matmul.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_matpow.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_permutation.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_sets.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_slice.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_special.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_trace.cpython-310.pyc,,
sympy/matrices/expressions/tests/__pycache__/test_transpose.cpython-310.pyc,,
sympy/matrices/expressions/tests/test_adjoint.py,sha256=cxOc334yNSI9MazhG9HT8s1OCXjkDWr3Zj2JnyHS3Z4,1065
sympy/matrices/expressions/tests/test_applyfunc.py,sha256=mxTJaoB4Ze50lk-2TgVopmrrbuQbEqUsZwc3K1H8w-Q,3522
sympy/matrices/expressions/tests/test_blockmatrix.py,sha256=ANNR7e2eiGIvabMNezxRVzipmA8oUwmDTrBTz5ALMzU,16541
sympy/matrices/expressions/tests/test_companion.py,sha256=Lam6r-cSOokjhSlJws55Kq-gL5_pHfeV_Xuvmn5PkRU,1657
sympy/matrices/expressions/tests/test_derivatives.py,sha256=9mBeaAZDX7-JbYs6tMClNuGDygETVN_dCXSlHmyAhwg,15991
sympy/matrices/expressions/tests/test_determinant.py,sha256=JSgptLz9KNC4_X27qnuFq-rscgHk6144s5TEUQpLxr0,2067
sympy/matrices/expressions/tests/test_diagonal.py,sha256=3L6Vs_Yr36a8dgIqAeIcNEf0xcVyeyGhANNu0dlIpwI,4516
sympy/matrices/expressions/tests/test_dotproduct.py,sha256=Zkv2N6oRPm0-sN4PFwsVFrM5Y_qv4x2gWqQQQD86hBY,1171
sympy/matrices/expressions/tests/test_factorizations.py,sha256=6UPA_UhCL5JPbaQCOatMnxhGnQ-aIHmb3lXqbwrSoIE,786
sympy/matrices/expressions/tests/test_fourier.py,sha256=0eD69faoHXBcuQ7g2Q31fqs-gyR_Xfe-gv-7DXhJh_c,1638
sympy/matrices/expressions/tests/test_funcmatrix.py,sha256=uN9r0ECMIBqsIzOezg_n9uDYNs6ebYS8Yf5yexUjmAM,2230
sympy/matrices/expressions/tests/test_hadamard.py,sha256=rR0l1howrI8SaJOnLb0fsCXS5cIx1rzahwFTfGldp3Y,4614
sympy/matrices/expressions/tests/test_indexing.py,sha256=wwYQa7LNlzhBA5fU50gPyE8cqaJf0s3O70PUx4eNCEA,12038
sympy/matrices/expressions/tests/test_inverse.py,sha256=n4gwv-GH0LPXZDVgzEB0lA_fk8MmNFK_BVNJb0FEUfY,2320
sympy/matrices/expressions/tests/test_kronecker.py,sha256=e5H6av3ioOn8jkjyDBrT3NEmCkyHbN6ZEHOlyB9OYLk,5366
sympy/matrices/expressions/tests/test_matadd.py,sha256=U1fL5YLP_cYEOsdi2uaGGrzm8qOsKcXn69BC1UV6RMM,1866
sympy/matrices/expressions/tests/test_matexpr.py,sha256=pQkxhi8okFDbF6Uea-dbeRrjNfmlc6nzJdIXOJxAqUI,18441
sympy/matrices/expressions/tests/test_matmul.py,sha256=GddV97hW1XerqwkI0snyI_HB6VVoZUwhqZ62DArmegc,5967
sympy/matrices/expressions/tests/test_matpow.py,sha256=dRbwvZ3vxwnqw09lnRqOu475f46xDPNo7V3oc1d_P2U,7308
sympy/matrices/expressions/tests/test_permutation.py,sha256=93Cqjj2k3aoR3ayMJLdJUa5h1u87bRRxT3I8B4FQsvU,5607
sympy/matrices/expressions/tests/test_sets.py,sha256=DfFGe6W1ppUs6bgo3vB3DSJvFemrT68s0F3QbyoIJiE,1408
sympy/matrices/expressions/tests/test_slice.py,sha256=C7OGAQQTz0YZxZCa7g0m8_0Bqq8jaPRa22JHVSqK7tY,2027
sympy/matrices/expressions/tests/test_special.py,sha256=Mhg71vnjjb4fm0jZgjDoWW8rAJMBeh8aDCM75gjEpKQ,6496
sympy/matrices/expressions/tests/test_trace.py,sha256=fRlrw9CfdO3z3SI4TQb1fCUb_zVAndbtyOErEeCTCQ0,3383
sympy/matrices/expressions/tests/test_transpose.py,sha256=P3wPPRywKnrAppX6gssgD66v0RIcolxqDkCaKGGPVcM,1987
sympy/matrices/expressions/trace.py,sha256=skr53LvstLV5Yg9hkaRb0yWrTdxC9u95G-YzIc80aTs,5362
sympy/matrices/expressions/transpose.py,sha256=QGQ1bgqvYmRNs6QiVolhtFlbluPYpwW3UNvkRZUqUHU,2645
sympy/matrices/graph.py,sha256=4UBv9SI5Z8Xjc5jPJVOoPVGQDtfbyOmS8f0ENrPOuU8,9080
sympy/matrices/immutable.py,sha256=okpJZ41FnaHp1PpwnPNCnB_o3afAU7DgRsr2NqBKtvg,5530
sympy/matrices/inverse.py,sha256=aVjDn_SjZUfi-jdM_1uJ6u6lVItuvbnb3CppayHQ-Gs,13166
sympy/matrices/kind.py,sha256=EJxDdD4gFgvVfC3lRex-bczhcGjwBhglf1hPDk2WzXE,2843
sympy/matrices/matrices.py,sha256=iqgi7x7cjnLT6OuRp6TYjoObJtUtbcPpeGTOC-Enh9Q,23536
sympy/matrices/matrixbase.py,sha256=rSimMyfKbweLO1lL9EumGY-vEUj5bkgrL1k7u4QIDZM,165072
sympy/matrices/normalforms.py,sha256=KiiKxxnYEaoA75UJjYFGqVLipgraNlG3Dlh9E2c1Q7k,3808
sympy/matrices/reductions.py,sha256=MZ8LtryawSm1jXsWzRPsYBmir867lRUWX4LFNGVIHwI,12500
sympy/matrices/repmatrix.py,sha256=Sw12DhMzKZ1_MC-SRx-5fiyuKQB60NKS5fFc4JDP-9A,29575
sympy/matrices/solvers.py,sha256=Xz6Oj35E-HbILbao2V0-UgZLA0s8bCs4mMSNGMK0xFc,25159
sympy/matrices/sparse.py,sha256=ER4tfnpvAMeockzWi10mKdKZJSYJNPISyoUL_m29UJ8,14673
sympy/matrices/sparsetools.py,sha256=tzI541P8QW_v1eVJAXgOlo_KK1Xp6u1geawX_tdlBxY,9182
sympy/matrices/subspaces.py,sha256=uLo4qnP0xvFcFo5hhf6g7pHSHiRbcQ1ATDKwGBxW7CE,3761
sympy/matrices/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/matrices/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/matrices/tests/__pycache__/test_commonmatrix.cpython-310.pyc,,
sympy/matrices/tests/__pycache__/test_decompositions.cpython-310.pyc,,
sympy/matrices/tests/__pycache__/test_determinant.cpython-310.pyc,,
sympy/matrices/tests/__pycache__/test_domains.cpython-310.pyc,,
sympy/matrices/tests/__pycache__/test_eigen.cpython-310.pyc,,
sympy/matrices/tests/__pycache__/test_graph.cpython-310.pyc,,
sympy/matrices/tests/__pycache__/test_immutable.cpython-310.pyc,,
sympy/matrices/tests/__pycache__/test_interactions.cpython-310.pyc,,
sympy/matrices/tests/__pycache__/test_matrices.cpython-310.pyc,,
sympy/matrices/tests/__pycache__/test_matrixbase.cpython-310.pyc,,
sympy/matrices/tests/__pycache__/test_normalforms.cpython-310.pyc,,
sympy/matrices/tests/__pycache__/test_reductions.cpython-310.pyc,,
sympy/matrices/tests/__pycache__/test_repmatrix.cpython-310.pyc,,
sympy/matrices/tests/__pycache__/test_solvers.cpython-310.pyc,,
sympy/matrices/tests/__pycache__/test_sparse.cpython-310.pyc,,
sympy/matrices/tests/__pycache__/test_sparsetools.cpython-310.pyc,,
sympy/matrices/tests/__pycache__/test_subspaces.cpython-310.pyc,,
sympy/matrices/tests/test_commonmatrix.py,sha256=CEYYa5frGDGPAVz78uYVTBC3IHoQLG18VRnq289XtBQ,40772
sympy/matrices/tests/test_decompositions.py,sha256=LTm3KjUHh0QkGmR8UA1CwGWzpFu8o-qcl4sv2VKBi8g,14419
sympy/matrices/tests/test_determinant.py,sha256=FxbLYbiO1wj12YYfsbQPoEOcgWpRGVu1CzVKLx4e8oQ,9560
sympy/matrices/tests/test_domains.py,sha256=gUnMLr_LeEsN5unFjIwHCZje0URC9uVBN_Q76JbX9f4,3276
sympy/matrices/tests/test_eigen.py,sha256=Xm-Xe7qGACLpqwOx5W2mDjve2KyJf-q8PyEi40jMgAk,22722
sympy/matrices/tests/test_graph.py,sha256=ckfGDCg2M6gluv9XFnfURga8gxd2HTL7aX281s6wy6c,3213
sympy/matrices/tests/test_immutable.py,sha256=JSu6YlGtPP-5iialCeatCKbZ4ScLDUzhQ-TMGhsalp8,4616
sympy/matrices/tests/test_interactions.py,sha256=6T6wkHyTW5v2fwg0rz2HULoDElfA_NttApU2t-pZFKI,2070
sympy/matrices/tests/test_matrices.py,sha256=pPaPHG1uzBUUoS8YChKINGxQqBEZwWaWu-7z35p2Puc,160577
sympy/matrices/tests/test_matrixbase.py,sha256=FQO9NX6eMv0lohMTxhLh5dK5SqNIWcC5gyrWB-y_-6I,168730
sympy/matrices/tests/test_normalforms.py,sha256=JQvFfp53MW8cJhxEkyNvsMmhhD7FVncAkjuGMXu5Fok,3009
sympy/matrices/tests/test_reductions.py,sha256=tKv_KufpVc6qwH-MDz8XLpgVS4z6snnlNdd1ECAQSXM,13385
sympy/matrices/tests/test_repmatrix.py,sha256=lim0a7mknVOr7OxrsUeZ2ftl4JFw92UQiOAHEAvjFOY,1781
sympy/matrices/tests/test_solvers.py,sha256=nSMdGj8MkxwOZePV2EHkDZUqeQfhAbe8alNqS_5a-Ck,21731
sympy/matrices/tests/test_sparse.py,sha256=GvXN6kBVldjqoR8WN8I_PjblKhRmyRWvVuLUgZEgugY,23281
sympy/matrices/tests/test_sparsetools.py,sha256=pjQR6UaEMR92NolB_IGZ9Umk6FPZjvI0vk1Fd4H_C5I,4877
sympy/matrices/tests/test_subspaces.py,sha256=poY6k6l2LSL7OCixQNGzrauLZIYbrjDul7J-yEE02S8,3465
sympy/matrices/utilities.py,sha256=mMnNsDTxGKqiG0JATsM4W9b5jglhacy-vmRw2aZojgY,2117
sympy/multipledispatch/__init__.py,sha256=aV2NC2cO_KmD6QFiwy4oC1D8fm3pFuPbaiTMeWmNWak,259
sympy/multipledispatch/__pycache__/__init__.cpython-310.pyc,,
sympy/multipledispatch/__pycache__/conflict.cpython-310.pyc,,
sympy/multipledispatch/__pycache__/core.cpython-310.pyc,,
sympy/multipledispatch/__pycache__/dispatcher.cpython-310.pyc,,
sympy/multipledispatch/__pycache__/utils.cpython-310.pyc,,
sympy/multipledispatch/conflict.py,sha256=rR6tKn58MfhMMKZ4ZrhVduylXd9f5PjT2TpzM9LMB6o,2117
sympy/multipledispatch/core.py,sha256=I4WOnmu1VtlaCnn2oD9R2-xckkYLRZPNFEWtCOTAYfM,2261
sympy/multipledispatch/dispatcher.py,sha256=A2I4upt4qNollXGpwzrqg7M0oKHJhZx1BUMIBnjRIow,12226
sympy/multipledispatch/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/multipledispatch/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/multipledispatch/tests/__pycache__/test_conflict.cpython-310.pyc,,
sympy/multipledispatch/tests/__pycache__/test_core.cpython-310.pyc,,
sympy/multipledispatch/tests/__pycache__/test_dispatcher.cpython-310.pyc,,
sympy/multipledispatch/tests/test_conflict.py,sha256=msNVSiikuPOqsEm_MMGmjsNbA2CAR0F1FZaHskzzo04,1786
sympy/multipledispatch/tests/test_core.py,sha256=UfH_7cyvZ6PHjdH8vmLG49CG7E30W8uxm3FthuMc1Jk,4048
sympy/multipledispatch/tests/test_dispatcher.py,sha256=saJPpGXLpLOuRfw-ekzZGzY-Rys0NsS5ke0n33i9j0U,6228
sympy/multipledispatch/utils.py,sha256=39wB9i8jNhlLFZyCTFnioLx5N_CNWv4r5VZwKrxswIE,3097
sympy/ntheory/__init__.py,sha256=Nl42gnfrf0WulKtF--OTBP-rhbdl-uV7NdXhzrRaCFo,2756
sympy/ntheory/__pycache__/__init__.cpython-310.pyc,,
sympy/ntheory/__pycache__/bbp_pi.cpython-310.pyc,,
sympy/ntheory/__pycache__/continued_fraction.cpython-310.pyc,,
sympy/ntheory/__pycache__/digits.cpython-310.pyc,,
sympy/ntheory/__pycache__/ecm.cpython-310.pyc,,
sympy/ntheory/__pycache__/egyptian_fraction.cpython-310.pyc,,
sympy/ntheory/__pycache__/elliptic_curve.cpython-310.pyc,,
sympy/ntheory/__pycache__/factor_.cpython-310.pyc,,
sympy/ntheory/__pycache__/generate.cpython-310.pyc,,
sympy/ntheory/__pycache__/modular.cpython-310.pyc,,
sympy/ntheory/__pycache__/multinomial.cpython-310.pyc,,
sympy/ntheory/__pycache__/partitions_.cpython-310.pyc,,
sympy/ntheory/__pycache__/primetest.cpython-310.pyc,,
sympy/ntheory/__pycache__/qs.cpython-310.pyc,,
sympy/ntheory/__pycache__/residue_ntheory.cpython-310.pyc,,
sympy/ntheory/bbp_pi.py,sha256=ILur1c9Ja-1F_blgnInUx-WopQ_WSvK-2OvNLEe2Zx8,5998
sympy/ntheory/continued_fraction.py,sha256=bQW7PvdgDtWnbpCmkOwyz3mNYPOXh9_ehq3_ZpJO8Rw,10717
sympy/ntheory/digits.py,sha256=ea3xSLy8RMaMHsekg8nq2gnVTug7SOvaDjw0lmB6NMU,3831
sympy/ntheory/ecm.py,sha256=XXwYl9rRaxc34MKI2jXLvkgqSOKWXvNhcrUXp-rDnAM,11702
sympy/ntheory/egyptian_fraction.py,sha256=hW886hPWJtARqgZIrH1WjZFC0uvf9CHxMIn0X9MWZro,6923
sympy/ntheory/elliptic_curve.py,sha256=ZT677EHi26BkZdRTLs1Tf4VSyMxDzJrwk1xECoMT7Qg,11544
sympy/ntheory/factor_.py,sha256=fS7xEcyOGLxa-QwerQ1-AMnXIFpM9P-EebhhJnXf5TU,77999
sympy/ntheory/generate.py,sha256=WHErRr2JSlUGK6i3_0Lvoa77lO4g4dWR9ieLC0RfLhU,33470
sympy/ntheory/modular.py,sha256=wYNfNr5S8DqipQNBLVMR7cPUNg7twM25xcLhXBD14I4,8474
sympy/ntheory/multinomial.py,sha256=rbm3STjgfRbNVbcPeH69qtWktthSCk0sC373NuDM6fU,5073
sympy/ntheory/partitions_.py,sha256=lwBsng6jtTgLksQQJFk3uYCnumJanIIrBbFoyzOJ7Vc,9013
sympy/ntheory/primetest.py,sha256=VJPwNt1oTcNc5orG7GO0o5k_1dG1SAea2_91aMWTAfo,23097
sympy/ntheory/qs.py,sha256=mPJIN3utMHDZulabGHFDu0ZvE_GWeKOS1VJH-WW_D9s,18344
sympy/ntheory/residue_ntheory.py,sha256=IEQivvILejnGtJpC1RGr6iVQWRs31ONLKjVuvtqFn9k,54206
sympy/ntheory/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/ntheory/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/ntheory/tests/__pycache__/test_bbp_pi.cpython-310.pyc,,
sympy/ntheory/tests/__pycache__/test_continued_fraction.cpython-310.pyc,,
sympy/ntheory/tests/__pycache__/test_digits.cpython-310.pyc,,
sympy/ntheory/tests/__pycache__/test_ecm.cpython-310.pyc,,
sympy/ntheory/tests/__pycache__/test_egyptian_fraction.cpython-310.pyc,,
sympy/ntheory/tests/__pycache__/test_elliptic_curve.cpython-310.pyc,,
sympy/ntheory/tests/__pycache__/test_factor_.cpython-310.pyc,,
sympy/ntheory/tests/__pycache__/test_generate.cpython-310.pyc,,
sympy/ntheory/tests/__pycache__/test_hypothesis.cpython-310.pyc,,
sympy/ntheory/tests/__pycache__/test_modular.cpython-310.pyc,,
sympy/ntheory/tests/__pycache__/test_multinomial.cpython-310.pyc,,
sympy/ntheory/tests/__pycache__/test_partitions.cpython-310.pyc,,
sympy/ntheory/tests/__pycache__/test_primetest.cpython-310.pyc,,
sympy/ntheory/tests/__pycache__/test_qs.cpython-310.pyc,,
sympy/ntheory/tests/__pycache__/test_residue.cpython-310.pyc,,
sympy/ntheory/tests/test_bbp_pi.py,sha256=TgNpZOtKAfU-IVzO62Ko7Oy3LLNfdzEo9gWMTTDZL6c,9486
sympy/ntheory/tests/test_continued_fraction.py,sha256=JJsyEXatgjNxYZTKkKubvymyVs0WDpLfbEiAY5SYk8g,3583
sympy/ntheory/tests/test_digits.py,sha256=mrTfwboMCQkiOEpMgYg8Nrk12WE5pEtpmbQthRVt4Xc,1968
sympy/ntheory/tests/test_ecm.py,sha256=yco77gknWe6co4VKTCoRNKHzd3jdqGKQWQFwuziYNWI,2290
sympy/ntheory/tests/test_egyptian_fraction.py,sha256=tpHcwteuuQAahcPqvgBm4Mwq-efzcHOn8mldijynjlE,2378
sympy/ntheory/tests/test_elliptic_curve.py,sha256=wc0EOsGo-qGpdevRq1o64htwTOT_YSUzUfyhJC-JVbg,624
sympy/ntheory/tests/test_factor_.py,sha256=FVojm__8vGSjwP0TF1KxjsJJdk9WTBMnpGPgo37teWw,23494
sympy/ntheory/tests/test_generate.py,sha256=lhnJPjlz1TYrDHJ4Jq0F64P4KV8C7ngKmm3Jxtz7Wsk,9868
sympy/ntheory/tests/test_hypothesis.py,sha256=Ztg-QoiBxpUp6euPy1RcPbF6yaLK_ij-Jcl637GGhNY,728
sympy/ntheory/tests/test_modular.py,sha256=g73sUXtYNxzbDcq5UnMWT8NodAU8unwRj_E-PpvJqDs,1425
sympy/ntheory/tests/test_multinomial.py,sha256=8uuj6XlatNyIILOpjJap13CMZmDwrCyGKn9LiIUiLV0,2344
sympy/ntheory/tests/test_partitions.py,sha256=OoZ9lgNu0yHqcbFe3QiE2A6-l5TBeWTbvS-xBhrpd50,1033
sympy/ntheory/tests/test_primetest.py,sha256=PObvQkcnrmzRejtw7755DuCML4VqiW2vcGUv7JF8gH0,9348
sympy/ntheory/tests/test_qs.py,sha256=ZCWiWiUULzLDTCz6CsolmVAdvZMZrz3wFrZXd-GtHfM,4481
sympy/ntheory/tests/test_residue.py,sha256=b_pFrWk8E9F54G2OStbNcjvnu7Qt73W-FbnZvGVsgZU,16566
sympy/parsing/__init__.py,sha256=KHuyDeHY1ifpVxT4aTOhomazCBYVIrKWd28jqp6YNJ8,125
sympy/parsing/__pycache__/__init__.cpython-310.pyc,,
sympy/parsing/__pycache__/ast_parser.cpython-310.pyc,,
sympy/parsing/__pycache__/mathematica.cpython-310.pyc,,
sympy/parsing/__pycache__/maxima.cpython-310.pyc,,
sympy/parsing/__pycache__/sym_expr.cpython-310.pyc,,
sympy/parsing/__pycache__/sympy_parser.cpython-310.pyc,,
sympy/parsing/ast_parser.py,sha256=iJvr6bhm1RjM5rhWzZA4c4LGTH5lAFazN5zu8y8q-aY,2734
sympy/parsing/autolev/Autolev.g4,sha256=980mo25mLWrQFmhRIg-aqIalUuwktYYaBGTXZ5_XZwA,4195
sympy/parsing/autolev/__init__.py,sha256=sp5hzv5siVW3xUmhkp0S0iaA0Cz-PVB0HO1zC04pxYs,3611
sympy/parsing/autolev/__pycache__/__init__.cpython-310.pyc,,
sympy/parsing/autolev/__pycache__/_build_autolev_antlr.cpython-310.pyc,,
sympy/parsing/autolev/__pycache__/_listener_autolev_antlr.cpython-310.pyc,,
sympy/parsing/autolev/__pycache__/_parse_autolev_antlr.cpython-310.pyc,,
sympy/parsing/autolev/_antlr/__init__.py,sha256=MQ4ZacpTuP-NmruFXKdWLQatoeVJQ8SaBQ2DnYvtyE8,203
sympy/parsing/autolev/_antlr/__pycache__/__init__.cpython-310.pyc,,
sympy/parsing/autolev/_antlr/__pycache__/autolevlexer.cpython-310.pyc,,
sympy/parsing/autolev/_antlr/__pycache__/autolevlistener.cpython-310.pyc,,
sympy/parsing/autolev/_antlr/__pycache__/autolevparser.cpython-310.pyc,,
sympy/parsing/autolev/_antlr/autolevlexer.py,sha256=K7HF_-5dUyAIv1_7GkhTmxqSCanEhCpzJG8fayAEB3Q,13609
sympy/parsing/autolev/_antlr/autolevlistener.py,sha256=EDb3XkH9Y7CLzxGM-tY-nGqxMGfBHVkqKdVCPxABgRE,12821
sympy/parsing/autolev/_antlr/autolevparser.py,sha256=BZYJ7IkurRmm44S50pYp_9JHCjT8fr1w5HeksAEPjtg,106291
sympy/parsing/autolev/_build_autolev_antlr.py,sha256=XOR44PCPo234I_Z1QnneSArY8aPpp4xP4-dycMalQQw,2590
sympy/parsing/autolev/_listener_autolev_antlr.py,sha256=P5XTo2UjkyDyx4d9kpmWIm6BoCXyOiED9s8Tr3w3Am4,104758
sympy/parsing/autolev/_parse_autolev_antlr.py,sha256=b9hIaluJUd1V2XIAp1erak6U-c-CwKyDLH1UkYQuvKE,1736
sympy/parsing/autolev/test-examples/README.txt,sha256=0C4m_nLROeV5J8nMfm3RYEfYgQJqmlHZaCpVD24boQY,528
sympy/parsing/autolev/test-examples/__pycache__/ruletest1.cpython-310.pyc,,
sympy/parsing/autolev/test-examples/__pycache__/ruletest10.cpython-310.pyc,,
sympy/parsing/autolev/test-examples/__pycache__/ruletest11.cpython-310.pyc,,
sympy/parsing/autolev/test-examples/__pycache__/ruletest12.cpython-310.pyc,,
sympy/parsing/autolev/test-examples/__pycache__/ruletest2.cpython-310.pyc,,
sympy/parsing/autolev/test-examples/__pycache__/ruletest3.cpython-310.pyc,,
sympy/parsing/autolev/test-examples/__pycache__/ruletest4.cpython-310.pyc,,
sympy/parsing/autolev/test-examples/__pycache__/ruletest5.cpython-310.pyc,,
sympy/parsing/autolev/test-examples/__pycache__/ruletest6.cpython-310.pyc,,
sympy/parsing/autolev/test-examples/__pycache__/ruletest7.cpython-310.pyc,,
sympy/parsing/autolev/test-examples/__pycache__/ruletest8.cpython-310.pyc,,
sympy/parsing/autolev/test-examples/__pycache__/ruletest9.cpython-310.pyc,,
sympy/parsing/autolev/test-examples/pydy-example-repo/__pycache__/chaos_pendulum.cpython-310.pyc,,
sympy/parsing/autolev/test-examples/pydy-example-repo/__pycache__/double_pendulum.cpython-310.pyc,,
sympy/parsing/autolev/test-examples/pydy-example-repo/__pycache__/mass_spring_damper.cpython-310.pyc,,
sympy/parsing/autolev/test-examples/pydy-example-repo/__pycache__/non_min_pendulum.cpython-310.pyc,,
sympy/parsing/autolev/test-examples/pydy-example-repo/chaos_pendulum.al,sha256=HpTcX2wXzLqmgpp8fcSqNweKjxljk43iYK0wQmBbCDI,690
sympy/parsing/autolev/test-examples/pydy-example-repo/chaos_pendulum.py,sha256=FSu4TP2BDTQjzYhMkcpRhXbb3kAD27XCyO_EoL55Ack,2274
sympy/parsing/autolev/test-examples/pydy-example-repo/double_pendulum.al,sha256=wjeeRdCS3Es6ldX9Ug5Du1uaijUTyoXpfTqmhL0uYfk,427
sympy/parsing/autolev/test-examples/pydy-example-repo/double_pendulum.py,sha256=uU9azTUGrY15BSDtw5T_V-7gmjyhHbXslzkmwBvFjGk,1583
sympy/parsing/autolev/test-examples/pydy-example-repo/mass_spring_damper.al,sha256=Gf7OhgRlwqUEXq7rkfbf89yWA23u4uIUJ-buXTyOuXM,505
sympy/parsing/autolev/test-examples/pydy-example-repo/mass_spring_damper.py,sha256=9ReCAqcUH5HYBgHmop9h5Zx54mfScWZN5L5F6rCHk4w,1366
sympy/parsing/autolev/test-examples/pydy-example-repo/non_min_pendulum.al,sha256=p5v40h1nVFrWNqnB0K7GiNQT0b-MqwayYjZxXOY4M8M,362
sympy/parsing/autolev/test-examples/pydy-example-repo/non_min_pendulum.py,sha256=DdxcWrm3HMQuyyY3Pk6sKHb4RXhQEM_EKY3HYZCP8ec,1503
sympy/parsing/autolev/test-examples/ruletest1.al,sha256=mDJ02Q1Qm-ShVmGoyjzSfgDJHUOuDrsUg3YMnkpKdUw,176
sympy/parsing/autolev/test-examples/ruletest1.py,sha256=eIKEFzEwkCFhPF0GTmf6SLuxXT384GqdCJnhiL2U0BQ,555
sympy/parsing/autolev/test-examples/ruletest10.al,sha256=jKpV8BgX91iQsQDLFOJyaS396AyE5YQlUMxih5o9RK0,781
sympy/parsing/autolev/test-examples/ruletest10.py,sha256=I1tsQcSAW6wqIguF-7lwlj9D4YZ8kCZqPqTKPUHR9oI,2726
sympy/parsing/autolev/test-examples/ruletest11.al,sha256=j_q7giq2KIuXVRLWwNlwIlpbhNO6SqBMnLGLcxIkzwk,188
sympy/parsing/autolev/test-examples/ruletest11.py,sha256=dYTRtXvMDXHiKzXHD2Sh0fcEukob3wr_GbSeqaZrrO8,475
sympy/parsing/autolev/test-examples/ruletest12.al,sha256=drr2NLrK1ewn4FjMppXycpAUNbZEQ0IAMsdVx8nxk6I,185
sympy/parsing/autolev/test-examples/ruletest12.py,sha256=ZG36s3PnkT0aKBM9Nx6H0sdJrtoLwaebU9386YSUql8,472
sympy/parsing/autolev/test-examples/ruletest2.al,sha256=d-QjPpW0lzugaGBg8F6pDl_5sZHOR_EDJ8EvWLcz4FY,237
sympy/parsing/autolev/test-examples/ruletest2.py,sha256=jrJfb0Jk2FP4GS5pDa0UB5ph0ijEVd1X8meKeZrTVng,820
sympy/parsing/autolev/test-examples/ruletest3.al,sha256=1TAaOe8GI8-yBWJddfIxwnvScHNmOjSzSaQn0RS_v5k,308
sympy/parsing/autolev/test-examples/ruletest3.py,sha256=O3K3IQo-HCjAIOSkfz3bDlst7dVUiRwhOZ0q_3jb5LU,1574
sympy/parsing/autolev/test-examples/ruletest4.al,sha256=qPGlPbdDRrzTDUBeWydAIa7mbjs2o3uX938QAsWJ7Qk,302
sympy/parsing/autolev/test-examples/ruletest4.py,sha256=WHod5yzKF4TNbEf4Yfxmx9WnimA7NOXqtTjZXR8FsP0,682
sympy/parsing/autolev/test-examples/ruletest5.al,sha256=VuiKjiFmLK3uEdho0m3pk-n0qm4SNLoLPMRJqjMJ4GY,516
sympy/parsing/autolev/test-examples/ruletest5.py,sha256=WvUtno1D3BrmFNPYYIBKR_gOA-PaHoxLlSTNDX67dcQ,1991
sympy/parsing/autolev/test-examples/ruletest6.al,sha256=-HwgTmh_6X3wHjo3PQi7378t8YdizRJClc5Eb5DmjhE,703
sympy/parsing/autolev/test-examples/ruletest6.py,sha256=vEO0jMOD-KIevAcVexmpvac0MGjN7O_dNipOBJJNzF0,1473
sympy/parsing/autolev/test-examples/ruletest7.al,sha256=wR9S9rTzO9fyKL6Ofgwzw8XCFCV_p2hBpYotC8TvADI,773
sympy/parsing/autolev/test-examples/ruletest7.py,sha256=_XvMrMe5r9RLopTrIqMGLhaYvHL1qjteWz9CKcotCL8,1696
sympy/parsing/autolev/test-examples/ruletest8.al,sha256=P7Nu3Pq2R1mKcuFRc9dRO5jJ1_e5fwWdtqYG8NHVVds,682
sympy/parsing/autolev/test-examples/ruletest8.py,sha256=8tgbwJ-ir0wiOCsgIFCAu4uD8SieYRrLoLzEfae5YQY,2690
sympy/parsing/autolev/test-examples/ruletest9.al,sha256=txtZ5RH2p1FvAe6etwetSCH8rLktnpk5z0W72sCOdAA,755
sympy/parsing/autolev/test-examples/ruletest9.py,sha256=GtqV-Wq2GGJzfblMscAz-KXCzs0P_4XqvA3FIdlPe04,1965
sympy/parsing/c/__init__.py,sha256=J9CvkNRY-qy6CA06GZYuwTuxdnqas6oUP2g0qLztGro,65
sympy/parsing/c/__pycache__/__init__.cpython-310.pyc,,
sympy/parsing/c/__pycache__/c_parser.cpython-310.pyc,,
sympy/parsing/c/c_parser.py,sha256=YkUdum1BfYLQdPiEwGvyfKGw7X3A8lcktqFZbb80os4,38091
sympy/parsing/fortran/__init__.py,sha256=KraiVw2qxIgYeMRTFjs1vkMi-hqqDkxUBv8Rc2gwkCI,73
sympy/parsing/fortran/__pycache__/__init__.cpython-310.pyc,,
sympy/parsing/fortran/__pycache__/fortran_parser.cpython-310.pyc,,
sympy/parsing/fortran/fortran_parser.py,sha256=RpNQR3eNx5vgfzdt0nEZDCB56kF__SnYMaqWN3zla00,11483
sympy/parsing/latex/LICENSE.txt,sha256=AHvDClj6QKmW53IEcSDeTq8x9REOT5w7X5P8374urKE,1075
sympy/parsing/latex/LaTeX.g4,sha256=fG0ZUQPwYQOIbcyaPDAkGvcfGs3ZwwMB8ZnKW5yHUDY,5821
sympy/parsing/latex/__init__.py,sha256=glhAEw3Bt-Kt3hDswsrKxrxszZbfRDhlKIStoATnsc0,2288
sympy/parsing/latex/__pycache__/__init__.cpython-310.pyc,,
sympy/parsing/latex/__pycache__/_build_latex_antlr.cpython-310.pyc,,
sympy/parsing/latex/__pycache__/_parse_latex_antlr.cpython-310.pyc,,
sympy/parsing/latex/__pycache__/errors.cpython-310.pyc,,
sympy/parsing/latex/_antlr/__init__.py,sha256=TAb79senorEsoYLCLwUa8wg8AUCHzmmZ7tLdi0XGNaE,384
sympy/parsing/latex/_antlr/__pycache__/__init__.cpython-310.pyc,,
sympy/parsing/latex/_antlr/__pycache__/latexlexer.cpython-310.pyc,,
sympy/parsing/latex/_antlr/__pycache__/latexparser.cpython-310.pyc,,
sympy/parsing/latex/_antlr/latexlexer.py,sha256=Y1hmY1VGL5FTSSlToTRQydPnyaLLNy1mDSWx76HaYwM,30502
sympy/parsing/latex/_antlr/latexparser.py,sha256=ZvonpvTS3vLSOVpas88M3CfNnUhPUDsCCPPk4wBYUGE,123655
sympy/parsing/latex/_build_latex_antlr.py,sha256=id_4pbcI4nAa0tHumN0lZX0Ubb-BaJ3czGwiQR_jZPE,2777
sympy/parsing/latex/_parse_latex_antlr.py,sha256=sVaO04oSeHe_TaMeM-6toheCR88G_RmJYpUIx-Sef1g,20712
sympy/parsing/latex/errors.py,sha256=adSpvQyWjTLsbN_2KHJ4HuXpY7_U9noeWiG0lskYLgE,45
sympy/parsing/latex/lark/__init__.py,sha256=hhhvfKRGP3ON36wRJwVfxMWw_GA6rl0JKsIzjuaUX38,120
sympy/parsing/latex/lark/__pycache__/__init__.cpython-310.pyc,,
sympy/parsing/latex/lark/__pycache__/latex_parser.cpython-310.pyc,,
sympy/parsing/latex/lark/__pycache__/transformer.cpython-310.pyc,,
sympy/parsing/latex/lark/grammar/greek_symbols.lark,sha256=-G8JGrBredhWAzCaurr1UmqgRMRrAJfs_pANub8kXyA,937
sympy/parsing/latex/lark/grammar/latex.lark,sha256=RTDKmvL-1rJr4ZcRgIg999ng6Uh4uQYnRgCJjJww95M,9644
sympy/parsing/latex/lark/latex_parser.py,sha256=WjzWdyLqfpmHIF8zXWpNceYjfN0fIb_RU5PzOosgAjE,4457
sympy/parsing/latex/lark/transformer.py,sha256=RFUN6RJ-heSRdpNRX3m4_a5NYKal5afwvw_aqiTe4uY,19801
sympy/parsing/mathematica.py,sha256=s1QPqintno5J-sO8Bbz9-liv43KfBCVfPZN5bWkFsOs,39643
sympy/parsing/maxima.py,sha256=DhTnXRSAceijyA1OAm86c6TyW9-aeUVoZEELGu0oZtY,1835
sympy/parsing/sym_expr.py,sha256=-hxarp961eyLtuwUhbg3D3qzy06HrEPZEYpGVcJzAv0,8895
sympy/parsing/sympy_parser.py,sha256=a1Wk9qE8GwOWx2nQ9uXV9l0SJFy7Qd2pIYglO9CP_x4,43484
sympy/parsing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/parsing/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/parsing/tests/__pycache__/test_ast_parser.cpython-310.pyc,,
sympy/parsing/tests/__pycache__/test_autolev.cpython-310.pyc,,
sympy/parsing/tests/__pycache__/test_c_parser.cpython-310.pyc,,
sympy/parsing/tests/__pycache__/test_custom_latex.cpython-310.pyc,,
sympy/parsing/tests/__pycache__/test_fortran_parser.cpython-310.pyc,,
sympy/parsing/tests/__pycache__/test_implicit_multiplication_application.cpython-310.pyc,,
sympy/parsing/tests/__pycache__/test_latex.cpython-310.pyc,,
sympy/parsing/tests/__pycache__/test_latex_deps.cpython-310.pyc,,
sympy/parsing/tests/__pycache__/test_latex_lark.cpython-310.pyc,,
sympy/parsing/tests/__pycache__/test_mathematica.cpython-310.pyc,,
sympy/parsing/tests/__pycache__/test_maxima.cpython-310.pyc,,
sympy/parsing/tests/__pycache__/test_sym_expr.cpython-310.pyc,,
sympy/parsing/tests/__pycache__/test_sympy_parser.cpython-310.pyc,,
sympy/parsing/tests/test_ast_parser.py,sha256=lcT8w7mn6UEZ8T-xfA4TqG4Mt7JxY00oHhOW7JtHQfY,803
sympy/parsing/tests/test_autolev.py,sha256=tQuUFa8YqVdsHPOcUhAwlMKB8Uk08HejDhDCda8lXs0,6647
sympy/parsing/tests/test_c_parser.py,sha256=VYl3K4if_23iIS-Be8MBSG0OKZo-6xgxHiN22laeAyo,155354
sympy/parsing/tests/test_custom_latex.py,sha256=G8g2S6XtQaDSzVuFcruiItaCQjoFeG6NHU6WP01g9GI,2036
sympy/parsing/tests/test_fortran_parser.py,sha256=SGbawrJ4a780TJAFVMONc7Y3Y8VYgVqsIHxVGaicbxE,11828
sympy/parsing/tests/test_implicit_multiplication_application.py,sha256=nPzLKcAJJaoZgdLoq1_CXhiWKFBH--p4t6dq4I3sV9A,7448
sympy/parsing/tests/test_latex.py,sha256=WvKNJ5mtxfzl-rBiE9hc2aEz81b5BmI3SKO7sRZiNbI,11765
sympy/parsing/tests/test_latex_deps.py,sha256=oe5vm2eIKn05ZiCcXUaO8X6HCcRmN1qCuTsz6tB7Qrk,426
sympy/parsing/tests/test_latex_lark.py,sha256=3fO9x-uLJiZ6joWKbW5nDruoNR0xQgFyQy3W_k-07d4,21452
sympy/parsing/tests/test_mathematica.py,sha256=vAxwquc8ArTQE9UNbsO21FqSa6J17sCF-A4vhTPXLY0,13395
sympy/parsing/tests/test_maxima.py,sha256=iIwnFm0lYD0-JcraUIymogqEMN3ji0c-0JeNFFGTEDs,1987
sympy/parsing/tests/test_sym_expr.py,sha256=-wNR7GwvJHVmPSZxSuAuoX1_FJk83O0tcDi09qYY6Jk,5668
sympy/parsing/tests/test_sympy_parser.py,sha256=5__CszZfy8DAl5JzfsLGsDECRjdT20a3p9cwYBXvAh8,12253
sympy/physics/__init__.py,sha256=F_yvUMCuBq3HR-3Ai6W4oktBsXRg8KdutFLwT9FFJlY,220
sympy/physics/__pycache__/__init__.cpython-310.pyc,,
sympy/physics/__pycache__/hydrogen.cpython-310.pyc,,
sympy/physics/__pycache__/matrices.cpython-310.pyc,,
sympy/physics/__pycache__/paulialgebra.cpython-310.pyc,,
sympy/physics/__pycache__/pring.cpython-310.pyc,,
sympy/physics/__pycache__/qho_1d.cpython-310.pyc,,
sympy/physics/__pycache__/secondquant.cpython-310.pyc,,
sympy/physics/__pycache__/sho.cpython-310.pyc,,
sympy/physics/__pycache__/wigner.cpython-310.pyc,,
sympy/physics/biomechanics/__init__.py,sha256=dG1IoRAnmfXvSyPciqJVrPn5LLnuvbVnBt78hBG0maQ,1520
sympy/physics/biomechanics/__pycache__/__init__.cpython-310.pyc,,
sympy/physics/biomechanics/__pycache__/_mixin.cpython-310.pyc,,
sympy/physics/biomechanics/__pycache__/activation.cpython-310.pyc,,
sympy/physics/biomechanics/__pycache__/curve.cpython-310.pyc,,
sympy/physics/biomechanics/__pycache__/musculotendon.cpython-310.pyc,,
sympy/physics/biomechanics/_mixin.py,sha256=0D3iBqlCRmR4HXKMxyC2LvYpKGHreOuZY5dXMJioQ4A,1493
sympy/physics/biomechanics/activation.py,sha256=8EjTxDJX1JP4misuBOBjh5RxBg5I0DD8wBGCFSF_LXw,25521
sympy/physics/biomechanics/curve.py,sha256=tZpp4wXtjA2TCKiV8tbhkk4XyiazyLUW5C6drgXubjI,63243
sympy/physics/biomechanics/musculotendon.py,sha256=OEcIcWodohcUu0dKj1BYonDx2meuWLNDlKu8w-bUl_8,58275
sympy/physics/biomechanics/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/physics/biomechanics/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/physics/biomechanics/tests/__pycache__/test_activation.cpython-310.pyc,,
sympy/physics/biomechanics/tests/__pycache__/test_curve.cpython-310.pyc,,
sympy/physics/biomechanics/tests/__pycache__/test_mixin.cpython-310.pyc,,
sympy/physics/biomechanics/tests/__pycache__/test_musculotendon.cpython-310.pyc,,
sympy/physics/biomechanics/tests/test_activation.py,sha256=hdnMsFBLjloJylu8-cLZ44oamORG3kSsI0q2-eLQ_-I,13395
sympy/physics/biomechanics/tests/test_curve.py,sha256=4S9GUCE9AYv0e6vAEkTg8LPYxiLkU0-Wcj-L7Dgbe7U,77795
sympy/physics/biomechanics/tests/test_mixin.py,sha256=ds-EoUCvfiSjVGnC_mBwjn5mI7z5W5wi2UTZZ4-pIIQ,1322
sympy/physics/biomechanics/tests/test_musculotendon.py,sha256=Ls59mtJQ83W0fdpDGFGNeTgtxL8yAZ6ODW96N9mvFtM,32906
sympy/physics/continuum_mechanics/__init__.py,sha256=7mSGwehjzPp4EAuc1Tnt31R0vBbZzB1pv_syPJkjvbI,123
sympy/physics/continuum_mechanics/__pycache__/__init__.cpython-310.pyc,,
sympy/physics/continuum_mechanics/__pycache__/beam.cpython-310.pyc,,
sympy/physics/continuum_mechanics/__pycache__/cable.cpython-310.pyc,,
sympy/physics/continuum_mechanics/__pycache__/truss.cpython-310.pyc,,
sympy/physics/continuum_mechanics/beam.py,sha256=tORCY1MVjDFIgo7yxFadhWE6t_fZjwVX9G_pqSRa6Zo,151992
sympy/physics/continuum_mechanics/cable.py,sha256=dx82StQ1DH0kUChRMeezZ2RC0p1oVuoMxTobzvrffA0,21770
sympy/physics/continuum_mechanics/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/physics/continuum_mechanics/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/physics/continuum_mechanics/tests/__pycache__/test_beam.cpython-310.pyc,,
sympy/physics/continuum_mechanics/tests/__pycache__/test_cable.cpython-310.pyc,,
sympy/physics/continuum_mechanics/tests/__pycache__/test_truss.cpython-310.pyc,,
sympy/physics/continuum_mechanics/tests/test_beam.py,sha256=qwCwo2yu6MbSRRtNRwDLEm7pWfdMb69zHg3Epvgx3_o,27489
sympy/physics/continuum_mechanics/tests/test_cable.py,sha256=R7bsP3pNVfBzTtWVC95Ghlr91MyUCf-esZpdDgGtmVg,3831
sympy/physics/continuum_mechanics/tests/test_truss.py,sha256=wgtF1GbQX5hzx20UrBg2ZyEvplVpsio3DiU8CS_bAk8,3269
sympy/physics/continuum_mechanics/truss.py,sha256=hkLLnXA9aRaE2YvA0ods7T40zJQXUc_jLI108NjhtTI,44962
sympy/physics/control/__init__.py,sha256=YJvO2_X0jo5ZcAmTLkbOf2RFl-QTEpxpSme1wWyJxrk,1158
sympy/physics/control/__pycache__/__init__.cpython-310.pyc,,
sympy/physics/control/__pycache__/control_plots.cpython-310.pyc,,
sympy/physics/control/__pycache__/lti.cpython-310.pyc,,
sympy/physics/control/control_plots.py,sha256=1aPhS89FBCXKhBh82b7pGVgwpY7TByvhXpl8AUYLzFI,32847
sympy/physics/control/lti.py,sha256=7DU28mR-WzMTdJWeaM5FjtgUvlQ5DfGw1J9rLomZp34,152174
sympy/physics/control/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/physics/control/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/physics/control/tests/__pycache__/test_control_plots.cpython-310.pyc,,
sympy/physics/control/tests/__pycache__/test_lti.cpython-310.pyc,,
sympy/physics/control/tests/test_control_plots.py,sha256=FsoeIPIOSaxsn512Z_ZPQCk0H9mLTd897BcYyKGxYXA,15662
sympy/physics/control/tests/test_lti.py,sha256=Zk6y0eYfal7vqBJhxHBu-vnG71zxBR_Co0g0mW3nUhE,81282
sympy/physics/hep/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/physics/hep/__pycache__/__init__.cpython-310.pyc,,
sympy/physics/hep/__pycache__/gamma_matrices.cpython-310.pyc,,
sympy/physics/hep/gamma_matrices.py,sha256=WlSHLUtMU7NrgLyKEvTntMSYxMZq1r_6o2kqUEAdPaA,24253
sympy/physics/hep/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/physics/hep/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/physics/hep/tests/__pycache__/test_gamma_matrices.cpython-310.pyc,,
sympy/physics/hep/tests/test_gamma_matrices.py,sha256=iKqICj0bP7EK0sSuYFsPdPkDTbHGa6J_LMPZAzv1j4o,14722
sympy/physics/hydrogen.py,sha256=R2wnNi1xB-WTQ8Z9aPUhX9Z8mQ8TdhCM1JAZIkyXgjw,7594
sympy/physics/matrices.py,sha256=jHfbWkzL2myFt-39kodQo5wPubBxNZKXlljuSxZL4bE,3836
sympy/physics/mechanics/__init__.py,sha256=pVjAF1iMoHI6397i9jB-L0_SC7CwDdKUzKcNB6c7uxM,2823
sympy/physics/mechanics/__pycache__/__init__.cpython-310.pyc,,
sympy/physics/mechanics/__pycache__/actuator.cpython-310.pyc,,
sympy/physics/mechanics/__pycache__/body.cpython-310.pyc,,
sympy/physics/mechanics/__pycache__/body_base.cpython-310.pyc,,
sympy/physics/mechanics/__pycache__/functions.cpython-310.pyc,,
sympy/physics/mechanics/__pycache__/inertia.cpython-310.pyc,,
sympy/physics/mechanics/__pycache__/joint.cpython-310.pyc,,
sympy/physics/mechanics/__pycache__/jointsmethod.cpython-310.pyc,,
sympy/physics/mechanics/__pycache__/kane.cpython-310.pyc,,
sympy/physics/mechanics/__pycache__/lagrange.cpython-310.pyc,,
sympy/physics/mechanics/__pycache__/linearize.cpython-310.pyc,,
sympy/physics/mechanics/__pycache__/loads.cpython-310.pyc,,
sympy/physics/mechanics/__pycache__/method.cpython-310.pyc,,
sympy/physics/mechanics/__pycache__/models.cpython-310.pyc,,
sympy/physics/mechanics/__pycache__/particle.cpython-310.pyc,,
sympy/physics/mechanics/__pycache__/pathway.cpython-310.pyc,,
sympy/physics/mechanics/__pycache__/rigidbody.cpython-310.pyc,,
sympy/physics/mechanics/__pycache__/system.cpython-310.pyc,,
sympy/physics/mechanics/__pycache__/wrapping_geometry.cpython-310.pyc,,
sympy/physics/mechanics/actuator.py,sha256=mn0TVd08FVEwWzazhMaEWdOXvMuQKfKpdd8fGbA7f5U,37353
sympy/physics/mechanics/body.py,sha256=Z9ZOReEab9FHEDsyv2RfIBQM4_ripXhFXmQtu1OmN_Y,24617
sympy/physics/mechanics/body_base.py,sha256=bwP04lWmD0iY_T0Vsn6NWMbnWyzUMFFqkAoAyKsow_c,2491
sympy/physics/mechanics/functions.py,sha256=KsX3z33rZdMCifwyVlyKIHVJh0Fve--zs5wd1rkGMl0,25190
sympy/physics/mechanics/inertia.py,sha256=XiPLKDYU0GR0HuBuRMcqGSALE0LObXfqZW0mE7vghA4,6152
sympy/physics/mechanics/joint.py,sha256=nb9MIqljHqD2NbTb2I_hkrJ1O2Nm2aP2rGzaJaCjpeU,84837
sympy/physics/mechanics/jointsmethod.py,sha256=nqkXawtuxeyP0D8DEAYURCJlDYS4Eka_vXx6UZP7y74,10415
sympy/physics/mechanics/kane.py,sha256=Pie17luGo7TohUXk3hZoPNsNheOSSIN5IFq1AA9UFeU,37171
sympy/physics/mechanics/lagrange.py,sha256=UTmClOP-PkY8S-LcPlcOAUmshndlq6mpmZailbvy4E4,20202
sympy/physics/mechanics/linearize.py,sha256=C2mqEXKnRU36iwO5NsHDHErgARcfnWrf15l-jSUA9QU,17241
sympy/physics/mechanics/loads.py,sha256=jnajZOg631Aqtd0-BplehohUL991C3Cji2OZZ3MVHdk,5406
sympy/physics/mechanics/method.py,sha256=2vFRhA79ra4HR6AzVBHMr3oNncrcqgLLMRqdyif0DrI,660
sympy/physics/mechanics/models.py,sha256=9q1g3I2xYpuTMi-v9geswEqxJWTP3RjcOquRfzMhHzM,6463
sympy/physics/mechanics/particle.py,sha256=YKiEBkPLVRI9foXlEe8eu8Ys5W1GyQO1oOBf0T8fHFg,5985
sympy/physics/mechanics/pathway.py,sha256=gBfjoSyjym7qz8tD1jD5IUV8KivTASSUI82d1wmHT50,26558
sympy/physics/mechanics/rigidbody.py,sha256=s5b7ynsdcMsq9PN2nM1yozNcZd08RA-MgzMjcs7jnBI,10287
sympy/physics/mechanics/system.py,sha256=VsPPQfJs0gBLKJYNQ2ZLmD98JWLd1IZy0QGRwI21_TM,59457
sympy/physics/mechanics/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/physics/mechanics/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/physics/mechanics/tests/__pycache__/test_actuator.cpython-310.pyc,,
sympy/physics/mechanics/tests/__pycache__/test_body.cpython-310.pyc,,
sympy/physics/mechanics/tests/__pycache__/test_functions.cpython-310.pyc,,
sympy/physics/mechanics/tests/__pycache__/test_inertia.cpython-310.pyc,,
sympy/physics/mechanics/tests/__pycache__/test_joint.cpython-310.pyc,,
sympy/physics/mechanics/tests/__pycache__/test_jointsmethod.cpython-310.pyc,,
sympy/physics/mechanics/tests/__pycache__/test_kane.cpython-310.pyc,,
sympy/physics/mechanics/tests/__pycache__/test_kane2.cpython-310.pyc,,
sympy/physics/mechanics/tests/__pycache__/test_kane3.cpython-310.pyc,,
sympy/physics/mechanics/tests/__pycache__/test_kane4.cpython-310.pyc,,
sympy/physics/mechanics/tests/__pycache__/test_kane5.cpython-310.pyc,,
sympy/physics/mechanics/tests/__pycache__/test_lagrange.cpython-310.pyc,,
sympy/physics/mechanics/tests/__pycache__/test_lagrange2.cpython-310.pyc,,
sympy/physics/mechanics/tests/__pycache__/test_linearize.cpython-310.pyc,,
sympy/physics/mechanics/tests/__pycache__/test_loads.cpython-310.pyc,,
sympy/physics/mechanics/tests/__pycache__/test_method.cpython-310.pyc,,
sympy/physics/mechanics/tests/__pycache__/test_models.cpython-310.pyc,,
sympy/physics/mechanics/tests/__pycache__/test_particle.cpython-310.pyc,,
sympy/physics/mechanics/tests/__pycache__/test_pathway.cpython-310.pyc,,
sympy/physics/mechanics/tests/__pycache__/test_rigidbody.cpython-310.pyc,,
sympy/physics/mechanics/tests/__pycache__/test_system.cpython-310.pyc,,
sympy/physics/mechanics/tests/__pycache__/test_system_class.cpython-310.pyc,,
sympy/physics/mechanics/tests/__pycache__/test_wrapping_geometry.cpython-310.pyc,,
sympy/physics/mechanics/tests/test_actuator.py,sha256=os3bFf8x25o1xA60N-8GY9QgPHKl9vN9hepFmRYTFPE,30206
sympy/physics/mechanics/tests/test_body.py,sha256=ygHfWeL6-3LW9gqXHfzdvqQhJWxg2-U2KQIHKqGNqKQ,12067
sympy/physics/mechanics/tests/test_functions.py,sha256=Cl1aT3qT-0Ik6vJgS6at6PUaeWRQfpEG88QvEIxz1Bk,10447
sympy/physics/mechanics/tests/test_inertia.py,sha256=On4K78tmq4tyr_QFs3Uo01efdlVj5R6yfn6CVs7ksnE,2726
sympy/physics/mechanics/tests/test_joint.py,sha256=ojhQdBsWN6nR7egEhpw-LAyDRdFIPfSEsDoFL1bhzqs,57922
sympy/physics/mechanics/tests/test_jointsmethod.py,sha256=8IUO7ntLPsydDT4YgA1FPyO5TXNpo4CIWC7O_JdWg9Q,10226
sympy/physics/mechanics/tests/test_kane.py,sha256=K1HOm7cuPzXeO9ZmW7oAHgu-jxOg1IFqyKB3YheAbus,21545
sympy/physics/mechanics/tests/test_kane2.py,sha256=qNLkbSV6UYclQzMDoRi_TJrJEz0-MUi7yf1e7XW4ba4,19256
sympy/physics/mechanics/tests/test_kane3.py,sha256=-9MbXLfYUDhA_9D2o4NrNLgDTOnKDLLsgryQW3AHacs,14959
sympy/physics/mechanics/tests/test_kane4.py,sha256=qJYUfvnq1F5UN_AQqTu_3BT5XqGceqxnCyP3d2gE04A,4709
sympy/physics/mechanics/tests/test_kane5.py,sha256=gZvAyxJ8fkkLtH60xHlb_Gxrbt9d7VmJTckXSGmF0j4,5693
sympy/physics/mechanics/tests/test_lagrange.py,sha256=iuHomulBF8MafLeorKGaLHUEF8CvFhXcxEtN0hk1akM,10119
sympy/physics/mechanics/tests/test_lagrange2.py,sha256=Wih2pM-AgR-TOX8z4mE3FtySWnGj39BGLNwAWaN1jaQ,1400
sympy/physics/mechanics/tests/test_linearize.py,sha256=6yFFGEhJW60fx9Ny1duc6eyvGDg6rtmJVo_V1mgHgGk,13273
sympy/physics/mechanics/tests/test_loads.py,sha256=Kw94kP0tfwKsV-jCDHGTQsyc-1dKQl3ABJfqJtR8AJg,2698
sympy/physics/mechanics/tests/test_method.py,sha256=L7CnsvbQC-U7ijbSZdu7DEr03p88OLj4IPvFJ_3kCDo,154
sympy/physics/mechanics/tests/test_models.py,sha256=GcsfCm5G4PPYQXsHCiAKI1dEW42RaZOh-x6aEouTYo4,5078
sympy/physics/mechanics/tests/test_particle.py,sha256=JL6QAA6T3POQkSutUnungrVkR3xt6ZVX-hp75-EufQw,2682
sympy/physics/mechanics/tests/test_pathway.py,sha256=oGCxlUOviyNc1GBMvhk5sYVZfu8C4o7lJMbqatBie3A,24944
sympy/physics/mechanics/tests/test_rigidbody.py,sha256=ezMW5BWt9cWdNeY1B9aYcL4NsPcVkaKZuUS1C7S1qPk,6188
sympy/physics/mechanics/tests/test_system.py,sha256=Dihu77qM5_QkDQg-zavHbVhh_nvaGEVztXgPNl2_enk,8700
sympy/physics/mechanics/tests/test_system_class.py,sha256=Xe4VLrxWaYL3oRwP2SBaanWf5DY46d2yPlwf9H1XJ4M,38219
sympy/physics/mechanics/tests/test_wrapping_geometry.py,sha256=aXwuEaprstnSW7BwLr3OKUyxSagRPO58L-tUMaq3I9s,10502
sympy/physics/mechanics/wrapping_geometry.py,sha256=XkI331oQcBme4jDmH0RqQDWh_pq49o38nbRsXwT9O5E,21599
sympy/physics/optics/__init__.py,sha256=0UmqIt2-u8WwNkAqsnOVt9VlkB9K0CRIJYiQaltJ73w,1647
sympy/physics/optics/__pycache__/__init__.cpython-310.pyc,,
sympy/physics/optics/__pycache__/gaussopt.cpython-310.pyc,,
sympy/physics/optics/__pycache__/medium.cpython-310.pyc,,
sympy/physics/optics/__pycache__/polarization.cpython-310.pyc,,
sympy/physics/optics/__pycache__/utils.cpython-310.pyc,,
sympy/physics/optics/__pycache__/waves.cpython-310.pyc,,
sympy/physics/optics/gaussopt.py,sha256=mVQ-JX7xmAp9XbNOYIlwsPAxkUukTw_QjbjIxuKWZW8,20898
sympy/physics/optics/medium.py,sha256=cys0tWGi1VCPWMTZuKadcN_bToz_bqKsDHSEVzuV3CE,7124
sympy/physics/optics/polarization.py,sha256=mIrZiOVXetGtKkLxl8Llaf2Z9coWenf6JKrClh4W8yU,21434
sympy/physics/optics/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/physics/optics/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/physics/optics/tests/__pycache__/test_gaussopt.cpython-310.pyc,,
sympy/physics/optics/tests/__pycache__/test_medium.cpython-310.pyc,,
sympy/physics/optics/tests/__pycache__/test_polarization.cpython-310.pyc,,
sympy/physics/optics/tests/__pycache__/test_utils.cpython-310.pyc,,
sympy/physics/optics/tests/__pycache__/test_waves.cpython-310.pyc,,
sympy/physics/optics/tests/test_gaussopt.py,sha256=QMXJw_6mFCC3918b-pc_4b_zgO8Hsk7_SBvMupbEi5I,4222
sympy/physics/optics/tests/test_medium.py,sha256=RxG7N3lzmCO_8hIoKyPnDKffmk8QFzA9yamu1_mr_dE,2194
sympy/physics/optics/tests/test_polarization.py,sha256=81MzyA29HZckg_Ss-88-5o0g9augDqCr_LwcJIiXuA0,2605
sympy/physics/optics/tests/test_utils.py,sha256=SjicjAptcZGwuX-ib_Lq7PlGONotvo2XJ4p3JA9iNVI,8553
sympy/physics/optics/tests/test_waves.py,sha256=PeFfrl7MBkWBHdc796sDDYDuhGepat3DQk7PmyTXVnw,3397
sympy/physics/optics/utils.py,sha256=BqfuvtrjO3PEcDQ1DecNyt2Th9Yps6xued1tEY4ysvk,22172
sympy/physics/optics/waves.py,sha256=Iw-9gGksvWhPmQ_VepmI90ekKyzHdPlq6U41wdM4ikI,10042
sympy/physics/paulialgebra.py,sha256=1r_qDBbVyl836qIXlVDdoF89Z9wedGvWIkHAbwQaK-4,6002
sympy/physics/pring.py,sha256=SCMGGIcEhVoD7dwhY7_NWL1iKwo7OfgKdmm2Ok_9Xl0,2240
sympy/physics/qho_1d.py,sha256=ZXemUsa_b0rLtPVTUkgAkZQ1Ecu2eIZxaiNSSXW0PDk,2005
sympy/physics/quantum/__init__.py,sha256=RA2xbM7GhFq3dVNTna3odlTJYHqNerxjNeZ1kwigHiw,1705
sympy/physics/quantum/__pycache__/__init__.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/anticommutator.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/boson.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/cartesian.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/cg.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/circuitplot.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/circuitutils.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/commutator.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/constants.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/dagger.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/density.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/fermion.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/gate.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/grover.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/hilbert.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/identitysearch.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/innerproduct.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/matrixcache.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/matrixutils.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/operator.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/operatorordering.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/operatorset.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/pauli.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/piab.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/qapply.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/qasm.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/qexpr.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/qft.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/qubit.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/represent.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/sho1d.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/shor.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/spin.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/state.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/tensorproduct.cpython-310.pyc,,
sympy/physics/quantum/__pycache__/trace.cpython-310.pyc,,
sympy/physics/quantum/anticommutator.py,sha256=TH0mPF3Dk9mL5fa2heuampDpwWFxxh3HCcg4g2uNQ_E,4446
sympy/physics/quantum/boson.py,sha256=cEH8dcPXunognApc69Y6TSJRMZ63P20No6tB2xGHynQ,6313
sympy/physics/quantum/cartesian.py,sha256=9R9VDYLV1Xe-GkA9TQbj8PVlBLaD0fF6KXfHJ1ze5as,9092
sympy/physics/quantum/cg.py,sha256=WK7HkAIRFejQQLjRsCC7rH0L--0fmXAoeL1JdTHb3GA,23319
sympy/physics/quantum/circuitplot.py,sha256=SacQMhPyDhizKmGRNEs1vtXph8lR6bMn5bVJI4rJiXg,11799
sympy/physics/quantum/circuitutils.py,sha256=mrQNUDbwM3LV1NZ1EqVpXyOY2mOXCBVZW7cQTiCxUaM,13882
sympy/physics/quantum/commutator.py,sha256=7IiNnFYxxi9EfElCFtMLEQccb6nB-jIeq4x3IlIqzKs,7521
sympy/physics/quantum/constants.py,sha256=20VRATCkSprSnGFR5ejvMEYlWwEcv1B-dE3RPqPTQ9k,1420
sympy/physics/quantum/dagger.py,sha256=Hks6Pka5vU_uvBU2HQ3eU0uQ9cdL8FjSstNVSYPGCU4,2529
sympy/physics/quantum/density.py,sha256=vCH8c4Fu5lcrT0PsuBqEK7eWnyHtCRwVx4wSh3f07ME,9743
sympy/physics/quantum/fermion.py,sha256=1ipn3FItUJ_ruLnflpp9MN_6t5w8CgHAJRJCOsukGGI,4983
sympy/physics/quantum/gate.py,sha256=Iv7-qhSCe_An9qaJcYRDgwr8ClNraP47E75UlS5fCoQ,42588
sympy/physics/quantum/grover.py,sha256=17KC5MJR3cCavmzqRqi9dB5OFTOpsYjfrTZuv03HiuE,10452
sympy/physics/quantum/hilbert.py,sha256=qrja92vF7BUeSyHOLKVX8-XKcPGT7QaQMWrqWXjRNus,19632
sympy/physics/quantum/identitysearch.py,sha256=Zh_ji5J0YeAy2AezsQcHV9W2icWoaa3ZwTbfjCCQmJo,27607
sympy/physics/quantum/innerproduct.py,sha256=K4tmyWYMlgzkTTXjs82PzEC8VU4jm2J6Qic4YmAM7SQ,4279
sympy/physics/quantum/matrixcache.py,sha256=S6fPkkYmfX8ELBOc9EST-8XnQ1gtpSOBfd2KwLGKdYo,3587
sympy/physics/quantum/matrixutils.py,sha256=tGCCFWCNRezGPYtaZPplEO_ZrvBhArq6i321UXKqInA,8215
sympy/physics/quantum/operator.py,sha256=eVA97Qajb3MRvCzrGe_KSnt9v0KLXaBmprxnU3sHzYU,19556
sympy/physics/quantum/operatorordering.py,sha256=byAyZCNKTCeFWIFThmNx0NgdI4u32O4ydodYSa6Wrr8,10296
sympy/physics/quantum/operatorset.py,sha256=h8nkScpQcUzCO3zemqKpgQfJDWiBbfj33IJzcl4J2_4,9563
sympy/physics/quantum/pauli.py,sha256=lzxWFHXqxKWRiYK99QCo9zuVG9eVXiB8vFya7TvrVxQ,17250
sympy/physics/quantum/piab.py,sha256=Zjb2cRGniVDV6e35gjP4uEpI4w0C7YGQIEXReaq_z-E,1912
sympy/physics/quantum/qapply.py,sha256=Y8B4Ob1_RqV8C18g9vM8SRk9BhVWQTdzt10JlW2bb9U,7250
sympy/physics/quantum/qasm.py,sha256=UWpcUIBgkK55SmEBZlpmz-1KGHZvW7dNeSVG8tHr44A,6288
sympy/physics/quantum/qexpr.py,sha256=UD2gBfjYRnHcqKYk-Jhex8dOoxNProadx154vejvtB4,14005
sympy/physics/quantum/qft.py,sha256=ua4qBQAm-gi923lRRxOgAebTsTCoR93pz8ZHPXBdcus,6425
sympy/physics/quantum/qubit.py,sha256=4EpA-3-PMtOibeQmfLGDt6Nirfl5LQFgT2hMmqmk0xM,25989
sympy/physics/quantum/represent.py,sha256=b_mEm3q-gZbIV5x5Vl6pzfyJytqlp_a98xpfse2AfgI,18707
sympy/physics/quantum/sho1d.py,sha256=ZroR_FjxmjOmDcd0Fm04vWKTGCpvLaEu4NiuplKm708,20867
sympy/physics/quantum/shor.py,sha256=DVwPxLAPSr8t3F3aXJIPe4o5XSuQiE6a6eA6OYmdZFw,5504
sympy/physics/quantum/spin.py,sha256=j5o0_xEypwqbuOqNETleRbZ0kLRbNBzTvFPZkRjDwzI,72986
sympy/physics/quantum/state.py,sha256=W7vIhsDVJwFZHVeuNvjD9uWFffWJpdTDDjAO27hk4gg,31042
sympy/physics/quantum/tensorproduct.py,sha256=TREq8MeHMMmAJqU97GKssf0hPABmmiUv_SpF__Lbt5E,15099
sympy/physics/quantum/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/physics/quantum/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_anticommutator.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_boson.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_cartesian.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_cg.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_circuitplot.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_circuitutils.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_commutator.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_constants.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_dagger.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_density.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_fermion.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_gate.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_grover.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_hilbert.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_identitysearch.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_innerproduct.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_matrixutils.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_operator.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_operatorordering.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_operatorset.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_pauli.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_piab.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_printing.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_qapply.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_qasm.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_qexpr.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_qft.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_qubit.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_represent.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_sho1d.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_shor.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_spin.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_state.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_tensorproduct.cpython-310.pyc,,
sympy/physics/quantum/tests/__pycache__/test_trace.cpython-310.pyc,,
sympy/physics/quantum/tests/test_anticommutator.py,sha256=ckWHKwQFiAMWcDaYSa_26vi_GIsvs32_0O62I5lGsr8,1304
sympy/physics/quantum/tests/test_boson.py,sha256=BZjdrZ-F1QhyhDqfK4Zc1VEFBJi1PeiPjMpfBcHekfo,1676
sympy/physics/quantum/tests/test_cartesian.py,sha256=b8eBLwmL8ize-a30TMDkoWuDym02PvBjr7ayfLwaR_I,4112
sympy/physics/quantum/tests/test_cg.py,sha256=BF-2ybLhoAYOb0wfWFlgnobMzH20zTYJvW5Z7v46SYI,9159
sympy/physics/quantum/tests/test_circuitplot.py,sha256=c3v9wUzLHUH-eBVGj6_broVhHkioNwpaaApTDAJEflU,2096
sympy/physics/quantum/tests/test_circuitutils.py,sha256=GrJAWRQVH_l8EIHrj1ve2jtxske72IriQ3lo94fqrVQ,13187
sympy/physics/quantum/tests/test_commutator.py,sha256=keBstGDpNITFRr06uVFrka_Lje56g6oFoJQEpZXmnYw,2727
sympy/physics/quantum/tests/test_constants.py,sha256=KBmYPIF49Sq34lbzbFCZRYWSyIdhnR3AK3q-VbU6grU,338
sympy/physics/quantum/tests/test_dagger.py,sha256=xDj4KCDiLETtbdDCeWRlobBkBf4h246yEubKF61Myqg,2544
sympy/physics/quantum/tests/test_density.py,sha256=EyxiEgyc0nDSweJwI0JUwta7gZ81TVHCl7YDEosTrvI,9718
sympy/physics/quantum/tests/test_fermion.py,sha256=RK-J3nV1UO_9R5UyrBIp_qfWX-5iZ152aoyEllKWSIc,1636
sympy/physics/quantum/tests/test_gate.py,sha256=7oBX1HoWnrYtHjABRoqv_wQDB9B829E99fdcJzaqawM,12496
sympy/physics/quantum/tests/test_grover.py,sha256=uze62AG6H4x2MYJJA-EY3NtkqwvrDIQ2kONuvIRQiZ4,3640
sympy/physics/quantum/tests/test_hilbert.py,sha256=IGP6rc2-b3we9dRDbpRniFAhQwp_TYtMfFzxusAprx0,2643
sympy/physics/quantum/tests/test_identitysearch.py,sha256=3YGrXCsFLhLtN5MRyT5ZF8ELrSdkvDKTv6xKM4i2ims,17745
sympy/physics/quantum/tests/test_innerproduct.py,sha256=37tT8p6MhHjAYeoay1Zyv7gCs-DeZQi4VdwUH2IffDE,1483
sympy/physics/quantum/tests/test_matrixutils.py,sha256=3wmKKRhfRuwdQWitWE2mJEHr-TUKn6ixNb_wPWs8wRw,4116
sympy/physics/quantum/tests/test_operator.py,sha256=BZNYANH2w2xfOkqFA3oIS_Kl1KnwnDUroV7d9lQ3IdY,8164
sympy/physics/quantum/tests/test_operatorordering.py,sha256=SFvJfrBxreMgMB3PEpXGcTvO_113Pi1O-Jco-A9_aVI,2003
sympy/physics/quantum/tests/test_operatorset.py,sha256=DNfBeYBa_58kSG7PM5Ilo6xnzek8lSiAGX01uMFRYqI,2628
sympy/physics/quantum/tests/test_pauli.py,sha256=Bhsx_gj5cpYv4BhVJRQohxlKk_rcp4jHtSRlTP-m_xs,4940
sympy/physics/quantum/tests/test_piab.py,sha256=8ndnzyIsjF4AOu_9k6Yqap_1XUDTbiGnv7onJdrZBWA,1086
sympy/physics/quantum/tests/test_printing.py,sha256=wR45NMA2w242-qnAlMjyOPj2yvwDbCKuBDh_V2sekr8,30294
sympy/physics/quantum/tests/test_qapply.py,sha256=uHw3Crt5Lv0t6TV9jxmNwPVbiWGzFMaLZ8TJZfB1-Mg,6022
sympy/physics/quantum/tests/test_qasm.py,sha256=ZvMjiheWBceSmIM9LHOL5fiFUl6HsUo8puqdzywrhkc,2976
sympy/physics/quantum/tests/test_qexpr.py,sha256=emcGEqQeCv-kVJxyfX66TZxahJ8pYznFLE1fyyzeZGc,1517
sympy/physics/quantum/tests/test_qft.py,sha256=v-sGTaW9S-gcGTDAUPvjwd1kINF6rlI_u5Sf-Gso0r8,1931
sympy/physics/quantum/tests/test_qubit.py,sha256=LQNaOuvXc-glRifQBlsXattAQB-yKHvmNMw68_JoM_c,8957
sympy/physics/quantum/tests/test_represent.py,sha256=lEwzpL0fGxDGkojZ4_WoBAtCcA7aq2-S-i0Z0QrnTXg,5177
sympy/physics/quantum/tests/test_sho1d.py,sha256=nc75ZE5XXtrc88OcfB5mAGh01Wpf3d4Rbsu8vLJPTC8,4684
sympy/physics/quantum/tests/test_shor.py,sha256=3a3GCg6V5_mlJ2bltoXinGMGvlSxpq7GluapD_3SZaQ,666
sympy/physics/quantum/tests/test_spin.py,sha256=LOIPNGWalfPLL7DNAaiLCp4J_G1mZpUYmTCNx3kjqgw,344807
sympy/physics/quantum/tests/test_state.py,sha256=UjfOdwRzNXHK0AMhEaI431eMNjVUK7glqiGxOXJEC50,6741
sympy/physics/quantum/tests/test_tensorproduct.py,sha256=oOrP1aLrH15Iaf17PTCDBLLcjz4dsux5UiwtxOUE1K0,5143
sympy/physics/quantum/tests/test_trace.py,sha256=dbpTXcJArWRR_Hh5JTuy2GJIfgjVo6zS20o5mdVEGH4,3057
sympy/physics/quantum/trace.py,sha256=2ZqN9IEsz3LKHTLV8ZDwTK0sM5PfwL0p2sYet0N7Gis,6397
sympy/physics/secondquant.py,sha256=FvAm6mVUVVRxaYPzqn4qwhkZCvN8LA8xUFKjnkMpPdw,90400
sympy/physics/sho.py,sha256=K8P9FAdZr6UfQKYZO9TlhDUqUd3YsMekXCsKy2HhaY0,2480
sympy/physics/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/physics/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/physics/tests/__pycache__/test_clebsch_gordan.cpython-310.pyc,,
sympy/physics/tests/__pycache__/test_hydrogen.cpython-310.pyc,,
sympy/physics/tests/__pycache__/test_paulialgebra.cpython-310.pyc,,
sympy/physics/tests/__pycache__/test_physics_matrices.cpython-310.pyc,,
sympy/physics/tests/__pycache__/test_pring.cpython-310.pyc,,
sympy/physics/tests/__pycache__/test_qho_1d.cpython-310.pyc,,
sympy/physics/tests/__pycache__/test_secondquant.cpython-310.pyc,,
sympy/physics/tests/__pycache__/test_sho.cpython-310.pyc,,
sympy/physics/tests/test_clebsch_gordan.py,sha256=YCIVSye68jYWiwNhYyT9CpRiqkOs8gjUILQAqBXu8OI,9223
sympy/physics/tests/test_hydrogen.py,sha256=kohRIR6JojE_GWYnlzLsMMgdhoKd8whazs0mq7cCTQc,4987
sympy/physics/tests/test_paulialgebra.py,sha256=tyshEMsLNPR4iYzoAbPGZRZ-e_8t7GDP_xyjRyhepeQ,1477
sympy/physics/tests/test_physics_matrices.py,sha256=Dha8iQRhzxLcl7TKSA6QP0pnEcBoqtj_Ob6tx01SMwI,2948
sympy/physics/tests/test_pring.py,sha256=XScQQO9RhRrlqSII_ZyyOUpE-zs-7wphSFCZq2OuFnE,1261
sympy/physics/tests/test_qho_1d.py,sha256=LD9WU-Y5lW7bVM7MyCkSGW9MU2FZhVjMB5Zk848_q1M,1775
sympy/physics/tests/test_secondquant.py,sha256=VgG8NzcFmIkhFbKZpbjjzV4W5JOaJHGj9Ut8ugWM2UM,48450
sympy/physics/tests/test_sho.py,sha256=aIs1f3eo6hb4ErRU8xrr_h_yhTmRx-fQgv9n27SfsLM,693
sympy/physics/units/__init__.py,sha256=DVvWy9qNRm742NFGcBpybFY20ZK3BU7DWNbLMTXYiFo,12386
sympy/physics/units/__pycache__/__init__.cpython-310.pyc,,
sympy/physics/units/__pycache__/dimensions.cpython-310.pyc,,
sympy/physics/units/__pycache__/prefixes.cpython-310.pyc,,
sympy/physics/units/__pycache__/quantities.cpython-310.pyc,,
sympy/physics/units/__pycache__/unitsystem.cpython-310.pyc,,
sympy/physics/units/__pycache__/util.cpython-310.pyc,,
sympy/physics/units/definitions/__init__.py,sha256=F3RyZc1AjM2Ch5b27Tt-VYdZ1HAIWvhgtQQQTfMiN6w,7470
sympy/physics/units/definitions/__pycache__/__init__.cpython-310.pyc,,
sympy/physics/units/definitions/__pycache__/dimension_definitions.cpython-310.pyc,,
sympy/physics/units/definitions/__pycache__/unit_definitions.cpython-310.pyc,,
sympy/physics/units/definitions/dimension_definitions.py,sha256=5r_WDnyWFX0T8bTjDA6pnr5PqRKv5XGTm0LuJrZ6ffM,1745
sympy/physics/units/definitions/unit_definitions.py,sha256=05wpHmAtyQvuJBeuzWm3cDQ6UYviNtsi4kVc0hv8VHw,14680
sympy/physics/units/dimensions.py,sha256=QgvlZkePGS76e0LdSdHJJ5EVLVjX1FItNRhJbiMs560,20898
sympy/physics/units/prefixes.py,sha256=_q2f8gA-kckBG7TutTFQazTf15PCZqNnaTR1gKXRfsk,6260
sympy/physics/units/quantities.py,sha256=r5E231CULmsSEM7Rh7zfcTPuR85_X0CwRCVU_nDsek0,4671
sympy/physics/units/systems/__init__.py,sha256=jJuvdc15c83yl11IuvhyjijwOZ9m1JGgZOgKwKv2e2o,244
sympy/physics/units/systems/__pycache__/__init__.cpython-310.pyc,,
sympy/physics/units/systems/__pycache__/cgs.cpython-310.pyc,,
sympy/physics/units/systems/__pycache__/length_weight_time.cpython-310.pyc,,
sympy/physics/units/systems/__pycache__/mks.cpython-310.pyc,,
sympy/physics/units/systems/__pycache__/mksa.cpython-310.pyc,,
sympy/physics/units/systems/__pycache__/natural.cpython-310.pyc,,
sympy/physics/units/systems/__pycache__/si.cpython-310.pyc,,
sympy/physics/units/systems/cgs.py,sha256=gXbX8uuZo7lcYIENA-CpAnyS9WVQy-vRisxlQm-198A,3702
sympy/physics/units/systems/length_weight_time.py,sha256=DXIDSWdhjfxGLA0ldOziWhwQjzTAs7-VQTNCHzDvCgY,7004
sympy/physics/units/systems/mks.py,sha256=Z3eX9yWK9BdvEosCROK2qRKtKFYOjtQ50Jk6vFT7AQY,1546
sympy/physics/units/systems/mksa.py,sha256=U8cSI-maIuLJRvpKLBuZA8V19LDRYVc2I40Rao-wvjk,2002
sympy/physics/units/systems/natural.py,sha256=43Odvmtxdpbz8UcW_xoRE9ArJVVdF7dgdAN2ByDAXx4,909
sympy/physics/units/systems/si.py,sha256=YBPUuovW3-JBDZYuStXXRaC8cfzE3En3K5MjNy5pLJk,14478
sympy/physics/units/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/physics/units/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/physics/units/tests/__pycache__/test_dimensions.cpython-310.pyc,,
sympy/physics/units/tests/__pycache__/test_dimensionsystem.cpython-310.pyc,,
sympy/physics/units/tests/__pycache__/test_prefixes.cpython-310.pyc,,
sympy/physics/units/tests/__pycache__/test_quantities.cpython-310.pyc,,
sympy/physics/units/tests/__pycache__/test_unit_system_cgs_gauss.cpython-310.pyc,,
sympy/physics/units/tests/__pycache__/test_unitsystem.cpython-310.pyc,,
sympy/physics/units/tests/__pycache__/test_util.cpython-310.pyc,,
sympy/physics/units/tests/test_dimensions.py,sha256=lzkgGfEXMHxB8Izv7nRTN2uOEPh65LXPYaG8Kr5H05o,6122
sympy/physics/units/tests/test_dimensionsystem.py,sha256=s2_2RAJwOaPOTvyIiAO9SYap374ytZqWbatWkLCnbSU,2717
sympy/physics/units/tests/test_prefixes.py,sha256=Y3vlIReWGu8bwwZTrNGZSVoWYjhzgUZC33CDeyIvw48,2238
sympy/physics/units/tests/test_quantities.py,sha256=XPuM6ul7XrUHvQE7F8rvpoCaxT9N6TM2gX99qUM4gTA,19758
sympy/physics/units/tests/test_unit_system_cgs_gauss.py,sha256=JepTWt8yGdtv5dQ2AKUKb9fxpuYqLWOp0oOmzov9vfY,3173
sympy/physics/units/tests/test_unitsystem.py,sha256=1Xh78_8hbv-yP4ICWI_dUrOnk3cimlvP_VhO-EXOa7Q,3254
sympy/physics/units/tests/test_util.py,sha256=76PaLp_Cd9BAiry6VcWUD4Hrr68D6lTOkScWcLyhmL0,9355
sympy/physics/units/unitsystem.py,sha256=UXFcmQoI8Hl89v4ixEfh35g__o6AgQPzgvLJhCLIFtA,7618
sympy/physics/units/util.py,sha256=yH23oXMpBGoLFfXFGMiNUekV9G4aH0frmrQR4SDBT9k,9888
sympy/physics/vector/__init__.py,sha256=jZmrNB6ZfY7NOP8nx8GWcfI2Ixb2mv7lXuGHn63kyOw,985
sympy/physics/vector/__pycache__/__init__.cpython-310.pyc,,
sympy/physics/vector/__pycache__/dyadic.cpython-310.pyc,,
sympy/physics/vector/__pycache__/fieldfunctions.cpython-310.pyc,,
sympy/physics/vector/__pycache__/frame.cpython-310.pyc,,
sympy/physics/vector/__pycache__/functions.cpython-310.pyc,,
sympy/physics/vector/__pycache__/point.cpython-310.pyc,,
sympy/physics/vector/__pycache__/printing.cpython-310.pyc,,
sympy/physics/vector/__pycache__/vector.cpython-310.pyc,,
sympy/physics/vector/dyadic.py,sha256=K5A3JtRQYSF5heY4twXesOOp5AITlB3XqfYKLQcALLA,18222
sympy/physics/vector/fieldfunctions.py,sha256=1tzyV2iH6-UIPJ6W4UhgOZHTGxAbnWhmdTxbz12Z528,8593
sympy/physics/vector/frame.py,sha256=W7lVn334XKWYKnFEsOEz7sX7-z7kGt9TxMaadT9-oz0,57271
sympy/physics/vector/functions.py,sha256=b4dHZOxbvS77SMcSlCV2GTaOzWj0TQiUtDaO_dhj3Uw,24810
sympy/physics/vector/point.py,sha256=eoYuKAETZ_WqFhezTHjspwXDSAs_2J5qTldjbTfoikk,20565
sympy/physics/vector/printing.py,sha256=a1N-wziCnt4gHtY9luqe-CDW9aAtpZ0FcvWwQ0hMEEo,11790
sympy/physics/vector/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/physics/vector/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/physics/vector/tests/__pycache__/test_dyadic.cpython-310.pyc,,
sympy/physics/vector/tests/__pycache__/test_fieldfunctions.cpython-310.pyc,,
sympy/physics/vector/tests/__pycache__/test_frame.cpython-310.pyc,,
sympy/physics/vector/tests/__pycache__/test_functions.cpython-310.pyc,,
sympy/physics/vector/tests/__pycache__/test_output.cpython-310.pyc,,
sympy/physics/vector/tests/__pycache__/test_point.cpython-310.pyc,,
sympy/physics/vector/tests/__pycache__/test_printing.cpython-310.pyc,,
sympy/physics/vector/tests/__pycache__/test_vector.cpython-310.pyc,,
sympy/physics/vector/tests/test_dyadic.py,sha256=vCSLxo0pynrHrCCo7S4925lgRhoSm_Mr5YhIh_1bYcw,4265
sympy/physics/vector/tests/test_fieldfunctions.py,sha256=FUjh18QzB6dXSau9iHutb36o28faSa7T9sB0icpja-M,5825
sympy/physics/vector/tests/test_frame.py,sha256=S5xn5Q9dMj_ACIS9ff7ACTBS7sFFovi9DIov-crUsko,29683
sympy/physics/vector/tests/test_functions.py,sha256=qHGT0RR-vkAp1oF30TFNgaeuOGvXdZnK2aKZuRgrZHg,21127
sympy/physics/vector/tests/test_output.py,sha256=hgqlE-_zN_EPE_gIZ_v2uXB1y2auo39hReWpvFUm2QQ,2612
sympy/physics/vector/tests/test_point.py,sha256=5CTzT3a-igd33auAra4uusm0PYUc_whPtV7KAoZ4g5w,12373
sympy/physics/vector/tests/test_printing.py,sha256=qVBjz4f3TtDrduUYLNDrvlrzBVMBDqLo27JWsFHdX18,10967
sympy/physics/vector/tests/test_vector.py,sha256=bqU1ltS6UGbSo74KXMtvP1mOpqKQ6XSV19Wjw2QoNFc,10259
sympy/physics/vector/vector.py,sha256=ZZHgr9Sm-biJcbug2v-pY5LtY-b5l4mFC08Q8U9_lT8,24994
sympy/physics/wigner.py,sha256=9U-JEwpTz3OPJiir4-GAFP5tIyyAmpJuNbljbqL117Q,38706
sympy/plotting/__init__.py,sha256=hAdOjai8-laj79rLJ2HZbiW1okXlz0p1ck-CoeNU6m8,526
sympy/plotting/__pycache__/__init__.cpython-310.pyc,,
sympy/plotting/__pycache__/experimental_lambdify.cpython-310.pyc,,
sympy/plotting/__pycache__/plot.cpython-310.pyc,,
sympy/plotting/__pycache__/plot_implicit.cpython-310.pyc,,
sympy/plotting/__pycache__/plotgrid.cpython-310.pyc,,
sympy/plotting/__pycache__/series.cpython-310.pyc,,
sympy/plotting/__pycache__/textplot.cpython-310.pyc,,
sympy/plotting/__pycache__/utils.cpython-310.pyc,,
sympy/plotting/backends/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/plotting/backends/__pycache__/__init__.cpython-310.pyc,,
sympy/plotting/backends/__pycache__/base_backend.cpython-310.pyc,,
sympy/plotting/backends/base_backend.py,sha256=aof5cs8mWG6fl87DadR5sZBjY8ssMelOwKFWi27-FO0,14911
sympy/plotting/backends/matplotlibbackend/__init__.py,sha256=Mzsz43gkanid12G8qnOcwJNbbvqf6eGppn-IZyJCMto,162
sympy/plotting/backends/matplotlibbackend/__pycache__/__init__.cpython-310.pyc,,
sympy/plotting/backends/matplotlibbackend/__pycache__/matplotlib.cpython-310.pyc,,
sympy/plotting/backends/matplotlibbackend/matplotlib.py,sha256=9efu7ST_D3M-_fepZCPTcy8TdYEIX0PCkyocDGVfbPE,12548
sympy/plotting/backends/textbackend/__init__.py,sha256=nnV_C7JJ_kwGcQe2C-tpnK635W8vTuIf5Grvvmur0rQ,92
sympy/plotting/backends/textbackend/__pycache__/__init__.cpython-310.pyc,,
sympy/plotting/backends/textbackend/__pycache__/text.cpython-310.pyc,,
sympy/plotting/backends/textbackend/text.py,sha256=1ukArjwwQWUED9SB-1dmkB6YL5EcJ2rUosUf_NcBpXs,803
sympy/plotting/experimental_lambdify.py,sha256=xcBhlvZ2h20aI1MpUN6qAEpO075Dv132AWbQJ7l3Wzg,22828
sympy/plotting/intervalmath/__init__.py,sha256=fQV7sLZ9NHpZO5XGl2ZfqX56x-mdq-sYhtWEKLngHlU,479
sympy/plotting/intervalmath/__pycache__/__init__.cpython-310.pyc,,
sympy/plotting/intervalmath/__pycache__/interval_arithmetic.cpython-310.pyc,,
sympy/plotting/intervalmath/__pycache__/interval_membership.cpython-310.pyc,,
sympy/plotting/intervalmath/__pycache__/lib_interval.cpython-310.pyc,,
sympy/plotting/intervalmath/interval_arithmetic.py,sha256=jv5YolNs6pOawIhuSsTBVwgkgmdOFwPrGN_1KtjfcIs,15570
sympy/plotting/intervalmath/interval_membership.py,sha256=1VpO1T7UjvPxcMySC5GhZl8-VM_DxIirSWC3ZGmxIAY,2385
sympy/plotting/intervalmath/lib_interval.py,sha256=WY1qRtyub4MDJaZizw6cXQI5NMEIXBO9UEWPEI80aW8,14809
sympy/plotting/intervalmath/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/plotting/intervalmath/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/plotting/intervalmath/tests/__pycache__/test_interval_functions.cpython-310.pyc,,
sympy/plotting/intervalmath/tests/__pycache__/test_interval_membership.cpython-310.pyc,,
sympy/plotting/intervalmath/tests/__pycache__/test_intervalmath.cpython-310.pyc,,
sympy/plotting/intervalmath/tests/test_interval_functions.py,sha256=gdIo5z54tIbG8hDaGd3I8rBDP67oetMZWWdM-uvt1ec,9862
sympy/plotting/intervalmath/tests/test_interval_membership.py,sha256=D1KjcrLxAwOmDEUqA-8TCqkFWGtmeerR9KwmzS7tyjk,4216
sympy/plotting/intervalmath/tests/test_intervalmath.py,sha256=ndBMczrs6xYMN5RGnyCL9yq7pNUxrXHTSU1mdUsp5tU,9034
sympy/plotting/plot.py,sha256=QMatkNNv7WnmEdD7OBu9eN_DC_8oPKYy0kksWM8CIQA,40761
sympy/plotting/plot_implicit.py,sha256=1xIIr4eV1gIU4SkQ2k54PBZGUgYMCgsPttY-9uZ2eZM,7330
sympy/plotting/plotgrid.py,sha256=QZmbxY-fcgPuseYLnKVDFsoH6m57Cosvy4W7l4jIqWw,6115
sympy/plotting/pygletplot/__init__.py,sha256=DM7GURQbdSfcddHz23MxOShatBFc26tP_sd3G8pGCQE,3732
sympy/plotting/pygletplot/__pycache__/__init__.cpython-310.pyc,,
sympy/plotting/pygletplot/__pycache__/color_scheme.cpython-310.pyc,,
sympy/plotting/pygletplot/__pycache__/managed_window.cpython-310.pyc,,
sympy/plotting/pygletplot/__pycache__/plot.cpython-310.pyc,,
sympy/plotting/pygletplot/__pycache__/plot_axes.cpython-310.pyc,,
sympy/plotting/pygletplot/__pycache__/plot_camera.cpython-310.pyc,,
sympy/plotting/pygletplot/__pycache__/plot_controller.cpython-310.pyc,,
sympy/plotting/pygletplot/__pycache__/plot_curve.cpython-310.pyc,,
sympy/plotting/pygletplot/__pycache__/plot_interval.cpython-310.pyc,,
sympy/plotting/pygletplot/__pycache__/plot_mode.cpython-310.pyc,,
sympy/plotting/pygletplot/__pycache__/plot_mode_base.cpython-310.pyc,,
sympy/plotting/pygletplot/__pycache__/plot_modes.cpython-310.pyc,,
sympy/plotting/pygletplot/__pycache__/plot_object.cpython-310.pyc,,
sympy/plotting/pygletplot/__pycache__/plot_rotation.cpython-310.pyc,,
sympy/plotting/pygletplot/__pycache__/plot_surface.cpython-310.pyc,,
sympy/plotting/pygletplot/__pycache__/plot_window.cpython-310.pyc,,
sympy/plotting/pygletplot/__pycache__/util.cpython-310.pyc,,
sympy/plotting/pygletplot/color_scheme.py,sha256=NgPUamkldygfrIPj0LvC_1AzhscVtg18FSudElvFYB8,12522
sympy/plotting/pygletplot/managed_window.py,sha256=N7AKtM7ELfIJLie6zvI-J6-OQRBnMZu6AL1USz7hFEk,3072
sympy/plotting/pygletplot/plot.py,sha256=s-5AJB0KelHs9WGoFIVIdYrOoMXfdpnM5-G2cF8xzDQ,13352
sympy/plotting/pygletplot/plot_axes.py,sha256=Q9YN8W0Hd1PeflHLvOvSZ-hxeLU4Kq3nUFLYDC0x0E8,8655
sympy/plotting/pygletplot/plot_camera.py,sha256=myYtKbv1ov_ltgR34hf8BR76t3AwTSu4QFUY5YY-e1E,3928
sympy/plotting/pygletplot/plot_controller.py,sha256=MroJJSPCbBDT8gGs_GdqpV_KHsllMNJpxx0MU3vKJV8,6941
sympy/plotting/pygletplot/plot_curve.py,sha256=YwKA2lYC7IwCOQJaOVnww8AAG4P36cArgbC1iLV9OFI,2838
sympy/plotting/pygletplot/plot_interval.py,sha256=doqr2wxnrED4MJDlkxQ07GFvaagX36HUb77ly_vIuKQ,5431
sympy/plotting/pygletplot/plot_mode.py,sha256=Djq-ewVms_JoSriDpolDhhtttBJQdJO8BD4E0nyOWcQ,14156
sympy/plotting/pygletplot/plot_mode_base.py,sha256=3z3WjeN7TTslHJevhr3X_7HRHPgUleYSngu6285lR6k,11502
sympy/plotting/pygletplot/plot_modes.py,sha256=gKzJShz6OXa6EHKar8SuHWrELVznxg_s2d5IBQkkeYE,5352
sympy/plotting/pygletplot/plot_object.py,sha256=qGtzcKup4It1CqZ2jxA7FnorCua4S9I-B_7I3SHBjcQ,330
sympy/plotting/pygletplot/plot_rotation.py,sha256=K8MyudYRS2F-ku5blzkWg3q3goMDPUsXqzmHLDU2Uqc,1447
sympy/plotting/pygletplot/plot_surface.py,sha256=C0q9tzDmxzC1IpWiNKY4llzcopx6dhotGOLpK1N9m3s,3803
sympy/plotting/pygletplot/plot_window.py,sha256=5boC2Fkmk46-gWGqWzdTkPmTMNHHOpA0CnB9q946Hwc,4643
sympy/plotting/pygletplot/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/plotting/pygletplot/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/plotting/pygletplot/tests/__pycache__/test_plotting.cpython-310.pyc,,
sympy/plotting/pygletplot/tests/test_plotting.py,sha256=NisjR-yuBRJfQvjcb20skTR3yid2U3MhKHW6sy8RE10,2720
sympy/plotting/pygletplot/util.py,sha256=mzQQgDDbp04B03KyJrossLp8Yq72RJzjp-3ArfjbMH8,4621
sympy/plotting/series.py,sha256=GhXqh3t5xfLINq9OX7zSIdHxR5vqPooFEcYkE_4wWRQ,96563
sympy/plotting/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/plotting/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/plotting/tests/__pycache__/test_experimental_lambdify.cpython-310.pyc,,
sympy/plotting/tests/__pycache__/test_plot.cpython-310.pyc,,
sympy/plotting/tests/__pycache__/test_plot_implicit.cpython-310.pyc,,
sympy/plotting/tests/__pycache__/test_series.cpython-310.pyc,,
sympy/plotting/tests/__pycache__/test_textplot.cpython-310.pyc,,
sympy/plotting/tests/__pycache__/test_utils.cpython-310.pyc,,
sympy/plotting/tests/test_experimental_lambdify.py,sha256=EYshdXA5tAGWolaDX-nHAolp7xIJN4Oqb1Uc1C1IhJI,3127
sympy/plotting/tests/test_plot.py,sha256=CGdhqOUNKHPjbheCY6_iX2h4yP0qnu6XuDaeGUOMb44,48097
sympy/plotting/tests/test_plot_implicit.py,sha256=wurrO7ntsHb4tWYPVs5VogRrtcylevu0EceCSEwiWQg,5799
sympy/plotting/tests/test_region_and.png,sha256=EV0Lm4HtQPk_6eIWtPY4TPcQk-O7tkpdZIuLmFjGRaA,6864
sympy/plotting/tests/test_region_not.png,sha256=3O_9_nPW149FMULEcT5RqI2-k2H3nHELbfJADt2cO8k,7939
sympy/plotting/tests/test_region_or.png,sha256=5Bug09vyog-Cu3mky7pbtFjew5bMvbpe0ZXWsgDKfy4,8809
sympy/plotting/tests/test_region_xor.png,sha256=kucVWBA9A98OpcR4did5aLXUyoq4z0O4C3PM6dliBSw,10002
sympy/plotting/tests/test_series.py,sha256=RT_o-EDqF6i94sKz8oFCirVdk1OZHW_gn0e5OZOf3UY,65637
sympy/plotting/tests/test_textplot.py,sha256=VurTGeMjUfBLpLdoMqzJK9gbcShNb7f1OrAcRNyrtag,12761
sympy/plotting/tests/test_utils.py,sha256=FkcYZAFT8TnHRIbkknx9NvA3-LgR0ua7WFyzQEPsVIE,3602
sympy/plotting/textplot.py,sha256=Xc-bWvzVOq8QUn_BxDlJv9bryCpgzsgV73XoQQwVj0Q,5075
sympy/plotting/utils.py,sha256=Cimno9MQGjcbe4rQ6TN86r4rM2t62CgARqPtgF0mhR8,12259
sympy/polys/__init__.py,sha256=qXmDNr7noCUAtIs0rJJRHHFbHjy3uHIvXZDajcSePc4,5577
sympy/polys/__pycache__/__init__.cpython-310.pyc,,
sympy/polys/__pycache__/appellseqs.cpython-310.pyc,,
sympy/polys/__pycache__/compatibility.cpython-310.pyc,,
sympy/polys/__pycache__/constructor.cpython-310.pyc,,
sympy/polys/__pycache__/densearith.cpython-310.pyc,,
sympy/polys/__pycache__/densebasic.cpython-310.pyc,,
sympy/polys/__pycache__/densetools.cpython-310.pyc,,
sympy/polys/__pycache__/dispersion.cpython-310.pyc,,
sympy/polys/__pycache__/distributedmodules.cpython-310.pyc,,
sympy/polys/__pycache__/domainmatrix.cpython-310.pyc,,
sympy/polys/__pycache__/euclidtools.cpython-310.pyc,,
sympy/polys/__pycache__/factortools.cpython-310.pyc,,
sympy/polys/__pycache__/fglmtools.cpython-310.pyc,,
sympy/polys/__pycache__/fields.cpython-310.pyc,,
sympy/polys/__pycache__/galoistools.cpython-310.pyc,,
sympy/polys/__pycache__/groebnertools.cpython-310.pyc,,
sympy/polys/__pycache__/heuristicgcd.cpython-310.pyc,,
sympy/polys/__pycache__/modulargcd.cpython-310.pyc,,
sympy/polys/__pycache__/monomials.cpython-310.pyc,,
sympy/polys/__pycache__/multivariate_resultants.cpython-310.pyc,,
sympy/polys/__pycache__/orderings.cpython-310.pyc,,
sympy/polys/__pycache__/orthopolys.cpython-310.pyc,,
sympy/polys/__pycache__/partfrac.cpython-310.pyc,,
sympy/polys/__pycache__/polyclasses.cpython-310.pyc,,
sympy/polys/__pycache__/polyconfig.cpython-310.pyc,,
sympy/polys/__pycache__/polyerrors.cpython-310.pyc,,
sympy/polys/__pycache__/polyfuncs.cpython-310.pyc,,
sympy/polys/__pycache__/polymatrix.cpython-310.pyc,,
sympy/polys/__pycache__/polyoptions.cpython-310.pyc,,
sympy/polys/__pycache__/polyquinticconst.cpython-310.pyc,,
sympy/polys/__pycache__/polyroots.cpython-310.pyc,,
sympy/polys/__pycache__/polytools.cpython-310.pyc,,
sympy/polys/__pycache__/polyutils.cpython-310.pyc,,
sympy/polys/__pycache__/rationaltools.cpython-310.pyc,,
sympy/polys/__pycache__/ring_series.cpython-310.pyc,,
sympy/polys/__pycache__/rings.cpython-310.pyc,,
sympy/polys/__pycache__/rootisolation.cpython-310.pyc,,
sympy/polys/__pycache__/rootoftools.cpython-310.pyc,,
sympy/polys/__pycache__/solvers.cpython-310.pyc,,
sympy/polys/__pycache__/specialpolys.cpython-310.pyc,,
sympy/polys/__pycache__/sqfreetools.cpython-310.pyc,,
sympy/polys/__pycache__/subresultants_qq_zz.cpython-310.pyc,,
sympy/polys/agca/__init__.py,sha256=fahpWoG_0LgoqOXBnDBJS16Jj1fE1_VKG7edM3qZ2HE,130
sympy/polys/agca/__pycache__/__init__.cpython-310.pyc,,
sympy/polys/agca/__pycache__/extensions.cpython-310.pyc,,
sympy/polys/agca/__pycache__/homomorphisms.cpython-310.pyc,,
sympy/polys/agca/__pycache__/ideals.cpython-310.pyc,,
sympy/polys/agca/__pycache__/modules.cpython-310.pyc,,
sympy/polys/agca/extensions.py,sha256=YmtFs9C0s-4DNMXFtdX1hYVNlby18mAJzhJ5Aqickrw,9388
sympy/polys/agca/homomorphisms.py,sha256=gaMNV96pKUuYHZ8Bd7QOs27J1IbbJgkEjyWcTLe8GFI,21937
sympy/polys/agca/ideals.py,sha256=S6rBl3H-hdeI44ZbELwjjt1rKlrhK11AKb8Aas-OtCE,11073
sympy/polys/agca/modules.py,sha256=I7alyzovH530qfxsaauo7o46ZBlBXQnHLUFpMVbM0Pk,47243
sympy/polys/agca/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/polys/agca/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/polys/agca/tests/__pycache__/test_extensions.cpython-310.pyc,,
sympy/polys/agca/tests/__pycache__/test_homomorphisms.cpython-310.pyc,,
sympy/polys/agca/tests/__pycache__/test_ideals.cpython-310.pyc,,
sympy/polys/agca/tests/__pycache__/test_modules.cpython-310.pyc,,
sympy/polys/agca/tests/test_extensions.py,sha256=i3IHQNXQByFMCvjjyd_hwwJSCiUj0z1rRwS9WFK2AFc,6455
sympy/polys/agca/tests/test_homomorphisms.py,sha256=m0hFmcTzvZ8sZbbnWeENwzKyufpE9zWwZR-WCI4kdpU,4224
sympy/polys/agca/tests/test_ideals.py,sha256=w76qXO-_HN6LQbV7l3h7gJZsM-DZ2io2X-kPWiHYRNw,3788
sympy/polys/agca/tests/test_modules.py,sha256=HdfmcxdEVucEbtfmzVq8i_1wGojT5b5DE5VIfbTMx3k,13552
sympy/polys/appellseqs.py,sha256=hWeDKsKnJuAuPN_5IU6m1okurAq9xMt3LQgMehcvBKQ,8305
sympy/polys/benchmarks/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/polys/benchmarks/__pycache__/__init__.cpython-310.pyc,,
sympy/polys/benchmarks/__pycache__/bench_galoispolys.cpython-310.pyc,,
sympy/polys/benchmarks/__pycache__/bench_groebnertools.cpython-310.pyc,,
sympy/polys/benchmarks/__pycache__/bench_solvers.cpython-310.pyc,,
sympy/polys/benchmarks/bench_galoispolys.py,sha256=8RtN9ZQga2oxscVPPkMGB29Dz8UbskMS2szYtqZ69u0,1502
sympy/polys/benchmarks/bench_groebnertools.py,sha256=YqGDCzewRszCye_GnneDXMRNB38ORSpVu_Jn0ELIySo,803
sympy/polys/benchmarks/bench_solvers.py,sha256=gLrZguh6pE0E4_vM2GeOS5bHnrcSUQXqD0Qz9tItfmo,446778
sympy/polys/compatibility.py,sha256=5myt7qcz0ifyp8Lfj2RHsbcoUXffC_tZ2iuAHDIkThs,58063
sympy/polys/constructor.py,sha256=rv8hQgE8-P9QXUtOQmlEiyA4XD-fb1Qzd8bvH3UHQls,11373
sympy/polys/densearith.py,sha256=Jv1-9vlBrmNG0PHIh7oJ4Oxw_wpjDnBf3sZdK6mhisg,34096
sympy/polys/densebasic.py,sha256=Kag6cqlxHhHUMYUXpr9GHLWh4E1Mnw2Esoh6zx9r14E,35954
sympy/polys/densetools.py,sha256=Wxnx6R0nnaibOHEr4NKw4Bacawk6pclWlL-WNHd7mUU,27312
sympy/polys/dispersion.py,sha256=s6GIYnGA6U9jhGP7YXQQS8G3byG4-kPbr55BR6p-iz4,5740
sympy/polys/distributedmodules.py,sha256=t8pLIgDQs_dMecGXwybVYoLavofEy2DXhFS8N5gj5SU,21827
sympy/polys/domainmatrix.py,sha256=FmNqklNFQR1WrQYtP2r7jypw2IQadNKGP14EaUaxUqI,310
sympy/polys/domains/__init__.py,sha256=T6qPNkU1EJ6D5BnvyJSXJv4zeJ5MUT5RLsovMkkXS9E,1872
sympy/polys/domains/__pycache__/__init__.cpython-310.pyc,,
sympy/polys/domains/__pycache__/algebraicfield.cpython-310.pyc,,
sympy/polys/domains/__pycache__/characteristiczero.cpython-310.pyc,,
sympy/polys/domains/__pycache__/complexfield.cpython-310.pyc,,
sympy/polys/domains/__pycache__/compositedomain.cpython-310.pyc,,
sympy/polys/domains/__pycache__/domain.cpython-310.pyc,,
sympy/polys/domains/__pycache__/domainelement.cpython-310.pyc,,
sympy/polys/domains/__pycache__/expressiondomain.cpython-310.pyc,,
sympy/polys/domains/__pycache__/expressionrawdomain.cpython-310.pyc,,
sympy/polys/domains/__pycache__/field.cpython-310.pyc,,
sympy/polys/domains/__pycache__/finitefield.cpython-310.pyc,,
sympy/polys/domains/__pycache__/fractionfield.cpython-310.pyc,,
sympy/polys/domains/__pycache__/gaussiandomains.cpython-310.pyc,,
sympy/polys/domains/__pycache__/gmpyfinitefield.cpython-310.pyc,,
sympy/polys/domains/__pycache__/gmpyintegerring.cpython-310.pyc,,
sympy/polys/domains/__pycache__/gmpyrationalfield.cpython-310.pyc,,
sympy/polys/domains/__pycache__/groundtypes.cpython-310.pyc,,
sympy/polys/domains/__pycache__/integerring.cpython-310.pyc,,
sympy/polys/domains/__pycache__/modularinteger.cpython-310.pyc,,
sympy/polys/domains/__pycache__/mpelements.cpython-310.pyc,,
sympy/polys/domains/__pycache__/old_fractionfield.cpython-310.pyc,,
sympy/polys/domains/__pycache__/old_polynomialring.cpython-310.pyc,,
sympy/polys/domains/__pycache__/polynomialring.cpython-310.pyc,,
sympy/polys/domains/__pycache__/pythonfinitefield.cpython-310.pyc,,
sympy/polys/domains/__pycache__/pythonintegerring.cpython-310.pyc,,
sympy/polys/domains/__pycache__/pythonrational.cpython-310.pyc,,
sympy/polys/domains/__pycache__/pythonrationalfield.cpython-310.pyc,,
sympy/polys/domains/__pycache__/quotientring.cpython-310.pyc,,
sympy/polys/domains/__pycache__/rationalfield.cpython-310.pyc,,
sympy/polys/domains/__pycache__/realfield.cpython-310.pyc,,
sympy/polys/domains/__pycache__/ring.cpython-310.pyc,,
sympy/polys/domains/__pycache__/simpledomain.cpython-310.pyc,,
sympy/polys/domains/algebraicfield.py,sha256=qy5mLTlNYfcCiaB_1RZnp_cnjhR7OePw72628rivmVI,21639
sympy/polys/domains/characteristiczero.py,sha256=vHYRUXPrfJzDF8wrd1KSFqG8WzwfITP_eweA-SHPVYA,382
sympy/polys/domains/complexfield.py,sha256=yiYoEY63giZV0HOoUI8iXiuxkZHgK8H3TkN_7nwPFME,5842
sympy/polys/domains/compositedomain.py,sha256=DAo2ISA9XdOnYzFu8azuPIQAT9fyVwSM8Pe425vmvww,1642
sympy/polys/domains/domain.py,sha256=_g9z0pjT7IOq326-MNYey0xyK6YVLQUONK5UtZrGxJs,40576
sympy/polys/domains/domainelement.py,sha256=IrG-Mzv_VlCAmE-hmJVH_d77TrsfyaGGfJVmU8FFvlY,860
sympy/polys/domains/expressiondomain.py,sha256=AB7Mnd6kOLaS_yG4lMXZTpVPUuAHGcrd6RAjjFSVxNc,7593
sympy/polys/domains/expressionrawdomain.py,sha256=cXarD2jXi97FGNiqNiDqQlX0g764EW2M1PEbrveImnY,1448
sympy/polys/domains/field.py,sha256=tyOjEqABaOXXkaBEL0qLqyG4g5Ktnd782B_6xTCfia8,2591
sympy/polys/domains/finitefield.py,sha256=Sib9bdMLE3k4hq5KYiU0VYLK0eV0fYeoGHC7BvGyDWM,9642
sympy/polys/domains/fractionfield.py,sha256=BY6eOkcfIlrmL1aP4cXgcKMAp78VhbUM40a5JdgY1qM,5776
sympy/polys/domains/gaussiandomains.py,sha256=HEHKza7CiIFbO_jnfIbTbDn7xbCnsDd8J0k_zpTzm6s,19005
sympy/polys/domains/gmpyfinitefield.py,sha256=WgSLnALNOVVKH931WpVT28ZWDilsrTDG8DyMee2xR94,437
sympy/polys/domains/gmpyintegerring.py,sha256=qJ7w8K40cfzhztZtOuWlIL2DXa642xJcKIIxoAOlcSs,3037
sympy/polys/domains/gmpyrationalfield.py,sha256=dZjrfcWaUA-BHUtutzLOWPlOSNLYzBqSFeukER6L_bA,3178
sympy/polys/domains/groundtypes.py,sha256=hAla27w5ekoJR_8c-1Yo8vrEgIIggPc615tfe1udc9A,2102
sympy/polys/domains/integerring.py,sha256=4oy49xTi8hV6qh8CWUAoBcgn2aJqgqwyh7bZBsjGfwI,7442
sympy/polys/domains/modularinteger.py,sha256=k6gskb0eypXdrKJRxR3l_75mVjmICGnaOy7FdRMwG8E,6042
sympy/polys/domains/mpelements.py,sha256=4nQhuVsLiznwFoVeA15QRq3aVaNGrYzRca7PJdCXCq0,4890
sympy/polys/domains/old_fractionfield.py,sha256=TUVxyL2fS4QF3kgyW5EGfkl91ir3S1klu08UfZr3GuI,6226
sympy/polys/domains/old_polynomialring.py,sha256=KQcH58oHnHzOpDdWojZiLlHDqrAiUd4OAaBIZigqpyc,15982
sympy/polys/domains/polynomialring.py,sha256=kStXSAtq1b5Tk3vrEze7_E8UMn8bF91Goh7hVzhtax0,6153
sympy/polys/domains/pythonfinitefield.py,sha256=lWp266ErnuUPuo7k8ju3z2S5IresFInpJAl4Ihsq0pI,453
sympy/polys/domains/pythonintegerring.py,sha256=EH5s6nwaxmeaScLER_FfqPdhyuJnbjtBslHmgyOyEPs,2962
sympy/polys/domains/pythonrational.py,sha256=M3VUGODh3MLElePjYtjt9b02ReMThw-XXpuQTkohgNs,548
sympy/polys/domains/pythonrationalfield.py,sha256=x8BPkGKj0WPuwJzN2py5l9aAjHaY4djv65c4tzUTr3Y,2295
sympy/polys/domains/quotientring.py,sha256=2NOUkkbFj4514qkDUszl6hl1EQuPikn3QEoQ1sDobGI,5911
sympy/polys/domains/rationalfield.py,sha256=D-pA-iHDHbOi6fivqSrJnfmH2JTrheKJQ9ZFXXN5ftM,5982
sympy/polys/domains/realfield.py,sha256=xgWEm8FM5_glr8-oErMX_2hasKzaS0eE9z_sn9DoKZE,5101
sympy/polys/domains/ring.py,sha256=p66U2X58acSHLHxOTU6aJZ0Umdcu1qiGIUDtV8iJCD0,3236
sympy/polys/domains/simpledomain.py,sha256=_K-Zz8Opf505r3eHSrbPAlnGiGSjY_O4Cwa4OTeOSoY,369
sympy/polys/domains/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/polys/domains/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/polys/domains/tests/__pycache__/test_domains.cpython-310.pyc,,
sympy/polys/domains/tests/__pycache__/test_polynomialring.cpython-310.pyc,,
sympy/polys/domains/tests/__pycache__/test_quotientring.cpython-310.pyc,,
sympy/polys/domains/tests/test_domains.py,sha256=NG2w0sS5J87LmWWaRUh3vGjpWW_LfkqZkaupjRIpv5s,50365
sympy/polys/domains/tests/test_polynomialring.py,sha256=8nNFKuQeicwiBk6pnqPLNk0cnI61iK8Dpl-TZifvORc,2895
sympy/polys/domains/tests/test_quotientring.py,sha256=BYoq1CqI76RDSm0xQdp1v7Dv1n5sdcmes-b_y_AfW-0,1459
sympy/polys/euclidtools.py,sha256=hAsNupzfR-07fTwFvTF2yDabyKMm_PLgFC8KX9c2CAo,41808
sympy/polys/factortools.py,sha256=CupycipegP1IGCYPY9eY2T8f_6h4gmr6qId1A4UDJvk,43034
sympy/polys/fglmtools.py,sha256=Z3VS_IKsZu1o3g22KPTIH1feNL8cTpieaNWvLuILmK4,4298
sympy/polys/fields.py,sha256=HEXUOH-bhYkTTXyev87LZPsyK3-aeqCmGRgErFiJzhA,21245
sympy/polys/galoistools.py,sha256=Z3ed_A0Nohj9RzuM3kom7-ay4MnzIHppwgV2QpONuTo,57238
sympy/polys/groebnertools.py,sha256=NhK-XcFR9e4chDDJJ-diXb7XYuw9zcixFA_riomThPM,23342
sympy/polys/heuristicgcd.py,sha256=rD3intgKCtAAMH3sqlgqbJL1XSq9QjfeG_MYzwCOek0,3732
sympy/polys/matrices/__init__.py,sha256=ZaPJMi8l22d3F3rudS4NqzSt0xwxbs3uwnQwlhhR91o,397
sympy/polys/matrices/__pycache__/__init__.cpython-310.pyc,,
sympy/polys/matrices/__pycache__/_dfm.cpython-310.pyc,,
sympy/polys/matrices/__pycache__/_typing.cpython-310.pyc,,
sympy/polys/matrices/__pycache__/ddm.cpython-310.pyc,,
sympy/polys/matrices/__pycache__/dense.cpython-310.pyc,,
sympy/polys/matrices/__pycache__/dfm.cpython-310.pyc,,
sympy/polys/matrices/__pycache__/domainmatrix.cpython-310.pyc,,
sympy/polys/matrices/__pycache__/domainscalar.cpython-310.pyc,,
sympy/polys/matrices/__pycache__/eigen.cpython-310.pyc,,
sympy/polys/matrices/__pycache__/exceptions.cpython-310.pyc,,
sympy/polys/matrices/__pycache__/linsolve.cpython-310.pyc,,
sympy/polys/matrices/__pycache__/lll.cpython-310.pyc,,
sympy/polys/matrices/__pycache__/normalforms.cpython-310.pyc,,
sympy/polys/matrices/__pycache__/rref.cpython-310.pyc,,
sympy/polys/matrices/__pycache__/sdm.cpython-310.pyc,,
sympy/polys/matrices/_dfm.py,sha256=iPE7JHs_IhzKq2gjxoMXpmgkvATLxAxGBL2gabXiv2g,31084
sympy/polys/matrices/_typing.py,sha256=Egp2nMOaq-oJCW0bgNDxy1Bx6evTl8eMTpf4mzIw1s4,407
sympy/polys/matrices/ddm.py,sha256=otuYH67Pea2a7eXZiDnyBsKNwnwY8E_hXs6wcDAl4uU,27712
sympy/polys/matrices/dense.py,sha256=RYdogVHeWZoeO94Oal1mf1sJvj-JmyS_baRi8-d9Rms,23217
sympy/polys/matrices/dfm.py,sha256=Fj_uF4FskrwcBDNuRSV--AozCP2cYkUz-BMitcMlkRE,1241
sympy/polys/matrices/domainmatrix.py,sha256=rVOEIn7vh_RGeBulN1JxS3gMyUgZXG8c0Ono7itEcYY,111864
sympy/polys/matrices/domainscalar.py,sha256=sPXC-it46yun2r_tJkun4MIuVQQyQMQTVsTedId8Rj4,3778
sympy/polys/matrices/eigen.py,sha256=glArxs9_rTrE_ssz2fd2gKCTsguSyEHwoeQP82tmIcM,3015
sympy/polys/matrices/exceptions.py,sha256=ay3Lv21X3QqszysBN71xdr9KGQuC5kDBl90a2Sjx6pM,1351
sympy/polys/matrices/linsolve.py,sha256=p86jlJP9h3CxZX92xk2sh1WAvKBkB5_YD55k1oYpmGI,7534
sympy/polys/matrices/lll.py,sha256=p5q_rb5QvAuHsGRZAzP2Jf_h2vdC_Be4Q62lkaYxW-c,3550
sympy/polys/matrices/normalforms.py,sha256=Lx-z0DfAMcl65zlxhfq3Vat9k7ML6l2Hco_o_jAhKxw,13148
sympy/polys/matrices/rref.py,sha256=5YK_phB382K9JFVIydlH2IYpziirEDUnKzyl5BfS2_o,15532
sympy/polys/matrices/sdm.py,sha256=coOMmOp1NLCMT_AtqNYDzKAW3kHsbg2EUyTPJtrq7Xg,63554
sympy/polys/matrices/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/polys/matrices/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/polys/matrices/tests/__pycache__/test_ddm.cpython-310.pyc,,
sympy/polys/matrices/tests/__pycache__/test_dense.cpython-310.pyc,,
sympy/polys/matrices/tests/__pycache__/test_domainmatrix.cpython-310.pyc,,
sympy/polys/matrices/tests/__pycache__/test_domainscalar.cpython-310.pyc,,
sympy/polys/matrices/tests/__pycache__/test_eigen.cpython-310.pyc,,
sympy/polys/matrices/tests/__pycache__/test_inverse.cpython-310.pyc,,
sympy/polys/matrices/tests/__pycache__/test_linsolve.cpython-310.pyc,,
sympy/polys/matrices/tests/__pycache__/test_lll.cpython-310.pyc,,
sympy/polys/matrices/tests/__pycache__/test_normalforms.cpython-310.pyc,,
sympy/polys/matrices/tests/__pycache__/test_nullspace.cpython-310.pyc,,
sympy/polys/matrices/tests/__pycache__/test_rref.cpython-310.pyc,,
sympy/polys/matrices/tests/__pycache__/test_sdm.cpython-310.pyc,,
sympy/polys/matrices/tests/__pycache__/test_xxm.cpython-310.pyc,,
sympy/polys/matrices/tests/test_ddm.py,sha256=BcnUntz3XR2Xyff02l4Mk2EsxwYlIBzM4lFSRo9oZT8,16699
sympy/polys/matrices/tests/test_dense.py,sha256=ETC2h5yLKsPJ5GAgx_RUcWyR63iZx81pbtOZiWty1r0,9516
sympy/polys/matrices/tests/test_domainmatrix.py,sha256=mgVcyPdqNkzq-Kx3bI5XoYin8b2f0pKAfyIbej_29F8,48953
sympy/polys/matrices/tests/test_domainscalar.py,sha256=HEFEKX5tP6SZ83_91nvLmFqgHxbVdpqOP4ZwzfFbHnc,3740
sympy/polys/matrices/tests/test_eigen.py,sha256=T1lYZeW-0NwDxDOG6ZJLr-OICfxY2wa0fVHV2V6EXSk,3200
sympy/polys/matrices/tests/test_inverse.py,sha256=5DT5qvfS3MlrgNgeHnG8GLjgLnmhsxj94yR3cbtmEO8,5247
sympy/polys/matrices/tests/test_linsolve.py,sha256=ur1BFzlIQyBrO_-aL71lXsLC67nfec9G5o-mzW_TFY4,3373
sympy/polys/matrices/tests/test_lll.py,sha256=RGYTDGbLfFvAMTESv-S1kqSWzwtOIjmguqXO3yGCjH4,6624
sympy/polys/matrices/tests/test_normalforms.py,sha256=_4Cm3EJxHh3TEwF278uB7WQZweFWFsx3j0zc2AZFgDI,3036
sympy/polys/matrices/tests/test_nullspace.py,sha256=eAEDPlnVkKfFM9jn1gztOUQTos1Sm9qwH10C-t9uLUE,5418
sympy/polys/matrices/tests/test_rref.py,sha256=mWTIfKAUP3vkGKhffCrPHuC_0DdF-iH41cchlSN8Pqc,25982
sympy/polys/matrices/tests/test_sdm.py,sha256=fSE3bQlDU-atzTFTgp4AgAS3QL-7rvb_61stj3QGBnU,13415
sympy/polys/matrices/tests/test_xxm.py,sha256=KK3K7Glshfpb50qRjMkP1f5oGh__sH11fYpPmRDy7RQ,25551
sympy/polys/modulargcd.py,sha256=tFj8fbScUUTd3tL5AAEYfylKg6HX_AePZi_W2e57HN4,58700
sympy/polys/monomials.py,sha256=FofdoHK8-TYr-Tcq4HjxUI4ojs3NLBrLavEBMPHz30A,18694
sympy/polys/multivariate_resultants.py,sha256=6bwdF-lcUqtKoVDrnOKhm_PyPfo8w0Yyy_GtESatQFw,15248
sympy/polys/numberfields/__init__.py,sha256=ZfhC9MyfGfGUz_DT_rXasB-M_P2zUiZXOJUNh_Gtm8c,538
sympy/polys/numberfields/__pycache__/__init__.cpython-310.pyc,,
sympy/polys/numberfields/__pycache__/basis.cpython-310.pyc,,
sympy/polys/numberfields/__pycache__/exceptions.cpython-310.pyc,,
sympy/polys/numberfields/__pycache__/galois_resolvents.cpython-310.pyc,,
sympy/polys/numberfields/__pycache__/galoisgroups.cpython-310.pyc,,
sympy/polys/numberfields/__pycache__/minpoly.cpython-310.pyc,,
sympy/polys/numberfields/__pycache__/modules.cpython-310.pyc,,
sympy/polys/numberfields/__pycache__/primes.cpython-310.pyc,,
sympy/polys/numberfields/__pycache__/resolvent_lookup.cpython-310.pyc,,
sympy/polys/numberfields/__pycache__/subfield.cpython-310.pyc,,
sympy/polys/numberfields/__pycache__/utilities.cpython-310.pyc,,
sympy/polys/numberfields/basis.py,sha256=IPA6cSwz-53ClQwo-wkmRzfx9pRX4iBhiggdLMVSgJ0,8261
sympy/polys/numberfields/exceptions.py,sha256=kHb-aB4eBzG3SsIpYtL5wLExDSb8lIOpNq0tk3guFIA,1594
sympy/polys/numberfields/galois_resolvents.py,sha256=OL3u-G6sCwvZuBuuYQO0QpL-wWxtjxFaBjVQhtiQ_Z8,25006
sympy/polys/numberfields/galoisgroups.py,sha256=c4s6z_mEUkoWKezFrfjKiU2tZnEnxR4m1LYaagicVno,20671
sympy/polys/numberfields/minpoly.py,sha256=uMMy3Ddui5_oNUBS55JNLF5xAZywfJzUjINmWRw3_EU,27716
sympy/polys/numberfields/modules.py,sha256=4MJykT6gtT_LC033LWsHT_CM7nEydLISAdQ0yA_bhkQ,69243
sympy/polys/numberfields/primes.py,sha256=UXOkuMdnamVvHPQZxwilCbhdwNNNS7zQ4bwSFc5xGgk,23984
sympy/polys/numberfields/resolvent_lookup.py,sha256=qfLNKOz_WjtXwpVlfzy8EkD4gw12epx9npE9HsjyIdg,40411
sympy/polys/numberfields/subfield.py,sha256=fN2TEbQCuZ9-sPRgHD5UBqC5CWezP2uYXPyttFZeHfI,16498
sympy/polys/numberfields/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/polys/numberfields/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/polys/numberfields/tests/__pycache__/test_basis.cpython-310.pyc,,
sympy/polys/numberfields/tests/__pycache__/test_galoisgroups.cpython-310.pyc,,
sympy/polys/numberfields/tests/__pycache__/test_minpoly.cpython-310.pyc,,
sympy/polys/numberfields/tests/__pycache__/test_modules.cpython-310.pyc,,
sympy/polys/numberfields/tests/__pycache__/test_numbers.cpython-310.pyc,,
sympy/polys/numberfields/tests/__pycache__/test_primes.cpython-310.pyc,,
sympy/polys/numberfields/tests/__pycache__/test_subfield.cpython-310.pyc,,
sympy/polys/numberfields/tests/__pycache__/test_utilities.cpython-310.pyc,,
sympy/polys/numberfields/tests/test_basis.py,sha256=96BJ7e4oPDKXyvlRrUkiQxmHyjRGpOkAC7R3ln-jgNE,4580
sympy/polys/numberfields/tests/test_galoisgroups.py,sha256=3LFuMbV92VBFlqqEqjh37oQvmG8cgZ0pFxDCXUoYRL4,5036
sympy/polys/numberfields/tests/test_minpoly.py,sha256=IA0WH56vMXbSQpiml78jZes1M1XZSHDRARv5tM4VGTQ,22590
sympy/polys/numberfields/tests/test_modules.py,sha256=GU4166j_hMlB22uWxxIjV_ON8RsyvpaN7Ly3eK8_m8Y,22926
sympy/polys/numberfields/tests/test_numbers.py,sha256=M0vZIBnjPBHV4vFUnPBILaqiR_cgSuU50kFB-v7l1gA,5988
sympy/polys/numberfields/tests/test_primes.py,sha256=JhcAkaQMgjkOSziQ2jZApJ8b8oviil5cUy0hfFqNmZg,9779
sympy/polys/numberfields/tests/test_subfield.py,sha256=_aCbvukrahv-QyCwNT7EpTYC1u53yUlMhfGqV5GzW3Y,12215
sympy/polys/numberfields/tests/test_utilities.py,sha256=rQGEJWctcfzjUtMwRuyCHerSqEsoP5C3z1bnddJA17o,3661
sympy/polys/numberfields/utilities.py,sha256=IHKmfafE9tMGammfdtkyR4_IHb0mON-OBwitE0Ss1R0,13087
sympy/polys/orderings.py,sha256=IFieyj4LkFa7NDiGTZD3VwUY7mSN3GEjThKk0z5WJ1s,8500
sympy/polys/orthopolys.py,sha256=PIJg_Kml8MxmJs5QPCjGmRLjjlE_4QKhsWhU7-an7x0,10181
sympy/polys/partfrac.py,sha256=1gIc4sjXDrBsRwDx-VXd8K_ywPhtOaSUbq03zMf9DG4,14696
sympy/polys/polyclasses.py,sha256=ovH-U00Js-qmUcCwVpSyEVJygPrTl1Et-ZZ2oqPuhpk,94116
sympy/polys/polyconfig.py,sha256=mgfFpp9SU159tA_PM2o04WZyzMoWfOtWZugRcHnP42c,1598
sympy/polys/polyerrors.py,sha256=xByI-fqIHVYsYRm63NmHXlSSRCwSI9vZUoO-1Mf5Wlk,4744
sympy/polys/polyfuncs.py,sha256=OEZpdYeHQADBJYqMw8JAyN4sw-jsJ6lzVH6m-CCoK8g,8547
sympy/polys/polymatrix.py,sha256=iNa-EyIrzv7ZeXMaP_PjojgNd29AnIotZE3NeF2te44,9771
sympy/polys/polyoptions.py,sha256=72KAYw1VINLtYPgZwId6gvzvgc7X0Fr7KDIJTiGwWKs,21670
sympy/polys/polyquinticconst.py,sha256=mYLFWSBq3H3Y0I8cx76Z_xauLx1YeViC4xF6yWsSTPQ,96035
sympy/polys/polyroots.py,sha256=bJToZlR6WZNl9fNxkIJxiPt3XoDQ_K6Pb3UOCBPfsKM,37025
sympy/polys/polytools.py,sha256=Yw6pAqH5MESN7SlIP0ObWzn8reaxDJkZxh1ho_zgU2I,205672
sympy/polys/polyutils.py,sha256=9E9RGSRlaKoN_n2_a7YPVSLiDo_hMZhVSHcvmlsj8Rc,17176
sympy/polys/rationaltools.py,sha256=8vbkg3nuBxbd4ztR7lOj2jTF9taKUKTPah_fD38Ex6c,2838
sympy/polys/ring_series.py,sha256=pDTNu05ChF_UQs8YxHr4uGpSjOgKB9qYTFXAJEsOFOA,57796
sympy/polys/rings.py,sha256=ATFeuECys4JaUnC6omGhmyR-K2qc43ChkHpIQ-ri9wA,83694
sympy/polys/rootisolation.py,sha256=fQGmgItykJ68WH-E35oQw6O7HVh763jThpJZFkIdjzY,64429
sympy/polys/rootoftools.py,sha256=MIjTAwU82mLgAM3UDfQ1AYSqm7h4UJjsGYQMTsEM9RM,41279
sympy/polys/solvers.py,sha256=FareKqDVVC6P2I4yjudi_3CS3kx0SbmAz3k7zHkETgE,13520
sympy/polys/specialpolys.py,sha256=B2vijl75zgUKUTY1HCqjB9BTDFf3FM8ugwkKGTB83XA,11038
sympy/polys/sqfreetools.py,sha256=RpnEw-nEo1j5PVtcs5xRAmGMYrwReq7HvZWuKCgRBLA,19876
sympy/polys/subresultants_qq_zz.py,sha256=TDVS9-rEBXK88m4mAixuvPFMAXmn3MwKaSsGmq9oUCo,88261
sympy/polys/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/polys/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_appellseqs.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_constructor.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_densearith.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_densebasic.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_densetools.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_dispersion.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_distributedmodules.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_euclidtools.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_factortools.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_fields.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_galoistools.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_groebnertools.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_heuristicgcd.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_hypothesis.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_injections.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_modulargcd.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_monomials.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_multivariate_resultants.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_orderings.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_orthopolys.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_partfrac.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_polyclasses.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_polyfuncs.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_polymatrix.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_polyoptions.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_polyroots.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_polytools.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_polyutils.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_pythonrational.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_rationaltools.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_ring_series.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_rings.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_rootisolation.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_rootoftools.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_solvers.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_specialpolys.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_sqfreetools.cpython-310.pyc,,
sympy/polys/tests/__pycache__/test_subresultants_qq_zz.cpython-310.pyc,,
sympy/polys/tests/test_appellseqs.py,sha256=YTERuRr30QtfxYR0erXvJG8D-INe9RaMFAF0ZM-H4Ks,3820
sympy/polys/tests/test_constructor.py,sha256=U1LBjA881oG4A8oMXqZe0sZ42pmH7YpR_VSJjBNZz-w,6378
sympy/polys/tests/test_densearith.py,sha256=98ozMHSqhbaexdU4yhZmBXcf2pmAt6E2_umcot9XsXQ,40469
sympy/polys/tests/test_densebasic.py,sha256=FcjAfZngYRQSWJ__BrBPITAUYjMNkS_pOtGyuJv_Bs0,21590
sympy/polys/tests/test_densetools.py,sha256=fwoYRhwCY1wyNExb5FXaFlqgncpJR5tqQuf-ZsJ2M4I,26345
sympy/polys/tests/test_dispersion.py,sha256=8JfwjSNy7X74qJODMaVp1GSLprFiRDVt6XrYc_-omgQ,3183
sympy/polys/tests/test_distributedmodules.py,sha256=dXmjhozX5Yzb7DsrtbdFTqAxi9Z1UZNJvGxj-vHM7cM,7639
sympy/polys/tests/test_euclidtools.py,sha256=vEyj48eIjm6-KRQtThNfI4ic_VDNB6l7jMouxJAF9HE,19482
sympy/polys/tests/test_factortools.py,sha256=K5_R-01Muc_45XOtolk_tQswE2l6tGrlOJ9zA_aNVYE,24903
sympy/polys/tests/test_fields.py,sha256=vrdg27319R3Zro_idhQVxIeomN9P6mU3jHyX7HZKeMU,10245
sympy/polys/tests/test_galoistools.py,sha256=0oN3eSWvV99juZSXco6Ek9n6s6BFOmOE4UIEhyZnQQs,28532
sympy/polys/tests/test_groebnertools.py,sha256=ZWHBcCCOVNwDxuJWg1WPo0krTHx1m1wTPi2cOYPsAT4,18584
sympy/polys/tests/test_heuristicgcd.py,sha256=87Yc0on955VExFyOgJuxBZhsIMFz1Vq4vclIVkQ--cE,4297
sympy/polys/tests/test_hypothesis.py,sha256=LdokFa3JrQ59_umY15x21QSScPjJoCl8RxHRfn1NUOc,1109
sympy/polys/tests/test_injections.py,sha256=EONGggBUNWaVSwi817CzLBYJgkTehFq8-m-Qdqes984,1286
sympy/polys/tests/test_modulargcd.py,sha256=GE-24EnWOAQVYwgBb5PJzySX6EEJQs-q3HRFBWsXkTE,9042
sympy/polys/tests/test_monomials.py,sha256=YcMBU1qe6g7EJojtV89dfJZmdXsXVTSly2J5cF6SD6o,11083
sympy/polys/tests/test_multivariate_resultants.py,sha256=DJu8CcZ3xwx8njpjDeSOyhyxeqZYmhfb7dkSCU-ll7Y,9501
sympy/polys/tests/test_orderings.py,sha256=bdsIsqJTFJCVyZNRMAGVDXVk79ldw9rmAGejS_lwKP0,4254
sympy/polys/tests/test_orthopolys.py,sha256=Ob5Osxd8hwebEjK3dDMarZ2VDXzl3pdsEdz_-kMa80M,6560
sympy/polys/tests/test_partfrac.py,sha256=VfWOx30fZu_avMbWqxtbLDcLi-9x6iuQmLfyatnFc3Y,7838
sympy/polys/tests/test_polyclasses.py,sha256=NwnvH3vfvIKyXXuhxLTbuu50emBXiecY_ZZ17hGvydU,14474
sympy/polys/tests/test_polyfuncs.py,sha256=VbgCgCRE06dtSY9I9GSdPH9T52ETYYoxk4J3N1WBtd4,4520
sympy/polys/tests/test_polymatrix.py,sha256=pl2VrN_d2XGOVHvvAnaNQzkdFTdQgjt9ePgo41soBRs,7353
sympy/polys/tests/test_polyoptions.py,sha256=z9DUdt8K3lYkm4IyLH1Cv-TKe76HP-EyaRkZVsfWb6U,12416
sympy/polys/tests/test_polyroots.py,sha256=m0rnjXMqX59XdFn9X4rRtZJjI39vBN-MAt9cpQszq-I,26803
sympy/polys/tests/test_polytools.py,sha256=P4a_pDqMoPQmnq2PmV9yqVzilHWYZsLKdnvmoosvw9o,131713
sympy/polys/tests/test_polyutils.py,sha256=Qs3QQl0WYmTnkYE2ovTxdLeu6DYnWO_OoUmLwNDZzSw,11547
sympy/polys/tests/test_pythonrational.py,sha256=vYMlOTuYvf-15P0nKTFm-uRrhUc-nCFEkqYFAPLxg08,4143
sympy/polys/tests/test_rationaltools.py,sha256=wkvjzNP1IH-SdubNk5JJ7OWcY-zNF6z3t32kfp9Ncs0,2397
sympy/polys/tests/test_ring_series.py,sha256=SCUiciL10XGGjxFuM6ulzA460XAUVRykW3HLb8RNsc0,24662
sympy/polys/tests/test_rings.py,sha256=qKCnwC-HKPBBn5eGczHv-7HVCBTJLuWFgZToOs5X1k0,48478
sympy/polys/tests/test_rootisolation.py,sha256=x-n-T-Con-8phelNa05BPszkC_UCW1C0yAOwz658I60,32724
sympy/polys/tests/test_rootoftools.py,sha256=ibXV6R79pVuyz_905yfHClAB1J78lMgRRP-rP2gIDX8,21916
sympy/polys/tests/test_solvers.py,sha256=LZwjEQKKpFdCr4hMaU0CoN650BqU-arsACJNOF7lOmk,13655
sympy/polys/tests/test_specialpolys.py,sha256=v1i0RMNlixxe2EbwqoE-UlV7a2KY-md4T7thhUhWSx0,5031
sympy/polys/tests/test_sqfreetools.py,sha256=m8ipJE28YC9eOrsVVAqxduCsw4iOW-s_nw76y0E8wUs,4886
sympy/polys/tests/test_subresultants_qq_zz.py,sha256=ro6-F0vJrR46syl5Q0zuXfXQzEREtlkWAeRV9xJE31Y,13138
sympy/printing/__init__.py,sha256=ws2P2KshXpwfnij4zaU3lVzIFQOh7nSjLbrB50cVFcU,2264
sympy/printing/__pycache__/__init__.cpython-310.pyc,,
sympy/printing/__pycache__/aesaracode.cpython-310.pyc,,
sympy/printing/__pycache__/c.cpython-310.pyc,,
sympy/printing/__pycache__/codeprinter.cpython-310.pyc,,
sympy/printing/__pycache__/conventions.cpython-310.pyc,,
sympy/printing/__pycache__/cxx.cpython-310.pyc,,
sympy/printing/__pycache__/defaults.cpython-310.pyc,,
sympy/printing/__pycache__/dot.cpython-310.pyc,,
sympy/printing/__pycache__/fortran.cpython-310.pyc,,
sympy/printing/__pycache__/glsl.cpython-310.pyc,,
sympy/printing/__pycache__/gtk.cpython-310.pyc,,
sympy/printing/__pycache__/jscode.cpython-310.pyc,,
sympy/printing/__pycache__/julia.cpython-310.pyc,,
sympy/printing/__pycache__/lambdarepr.cpython-310.pyc,,
sympy/printing/__pycache__/latex.cpython-310.pyc,,
sympy/printing/__pycache__/llvmjitcode.cpython-310.pyc,,
sympy/printing/__pycache__/maple.cpython-310.pyc,,
sympy/printing/__pycache__/mathematica.cpython-310.pyc,,
sympy/printing/__pycache__/mathml.cpython-310.pyc,,
sympy/printing/__pycache__/numpy.cpython-310.pyc,,
sympy/printing/__pycache__/octave.cpython-310.pyc,,
sympy/printing/__pycache__/precedence.cpython-310.pyc,,
sympy/printing/__pycache__/preview.cpython-310.pyc,,
sympy/printing/__pycache__/printer.cpython-310.pyc,,
sympy/printing/__pycache__/pycode.cpython-310.pyc,,
sympy/printing/__pycache__/python.cpython-310.pyc,,
sympy/printing/__pycache__/rcode.cpython-310.pyc,,
sympy/printing/__pycache__/repr.cpython-310.pyc,,
sympy/printing/__pycache__/rust.cpython-310.pyc,,
sympy/printing/__pycache__/smtlib.cpython-310.pyc,,
sympy/printing/__pycache__/str.cpython-310.pyc,,
sympy/printing/__pycache__/tableform.cpython-310.pyc,,
sympy/printing/__pycache__/tensorflow.cpython-310.pyc,,
sympy/printing/__pycache__/theanocode.cpython-310.pyc,,
sympy/printing/__pycache__/tree.cpython-310.pyc,,
sympy/printing/aesaracode.py,sha256=PnlFLJt8tUmrOz5_E3mfeMHqo4iXA8plDutZ1fZpc7c,18237
sympy/printing/c.py,sha256=_yBTfw-g5bCBR73DzHMTBwvQ7FQrjRmXjZUMFC9vUrw,27083
sympy/printing/codeprinter.py,sha256=JG-mt0kZYaWo3MQ5DblCTfAKaWbDagin9xkWfITXhPk,36122
sympy/printing/conventions.py,sha256=k6YRWHfvbLHJp1uKgQX-ySiOXSsXH8QJxC9fymYmcSM,2580
sympy/printing/cxx.py,sha256=vCQyzT-1eNLLDJy4NBwCp5X5OCoPOZ9icsx8YifaPsc,6123
sympy/printing/defaults.py,sha256=YitLfIRfFH8ltNd18Y6YtBgq5H2te0wFKlHuIO4cvo8,135
sympy/printing/dot.py,sha256=W0J798ZxBdlJercffBGnNDTp7J2tMdIYQkE_KIiyi3s,8274
sympy/printing/fortran.py,sha256=eWPirUxgm_cZcIX3-uSry10wrdaUzcMlh0HW8Q_f9nY,28707
sympy/printing/glsl.py,sha256=FvwQ3mOmyhGDCFOdTs8yR_B3TC-Q-4UQ6ompvh3Hadc,20386
sympy/printing/gtk.py,sha256=ptnwYxJr5ox3LG4TCDbRIgxsCikaVvEzWBaqIpITUXc,466
sympy/printing/jscode.py,sha256=Hm_43iVxeyuaTqYvawRAH0duxW_6bjpZBa_AozdwgcA,12053
sympy/printing/julia.py,sha256=gKsX0CrNZ3WQYzOt1WLL5osdgZJ79jtQ4BJcdGT7HV0,23475
sympy/printing/lambdarepr.py,sha256=BCx4eSdG8MQ8ZSUV1lWEd3CzbZ4IiMid-TTxPoV6FHU,8305
sympy/printing/latex.py,sha256=VXZp9x629k7RA0eBYaFpEI_LaF87nOmHZoC-3fwnhnY,123368
sympy/printing/llvmjitcode.py,sha256=r5K4hf1WVJvABrd1-alzv2nF3McuTq-GzSE-7j8xqps,17166
sympy/printing/maple.py,sha256=mWfI78Q8jII8ynS04cnnbq4G6hZNOOt-Q-DtRokwW20,10643
sympy/printing/mathematica.py,sha256=Q0mj0F7pGmjoPHmYWakubDAnWCv_TDtox11u2CWsRtg,12623
sympy/printing/mathml.py,sha256=eUr0mR_ftdKIY-2Rbm8bamFqKJn7Mha5AjPl7B0WEI4,73558
sympy/printing/numpy.py,sha256=Ww68XMahBGoiNMnIUSSxG6b6dREnU_KhnWBDvaQ2iZU,19983
sympy/printing/octave.py,sha256=RcIPHLGATi3CHiyPBudvY5zJAFEZm4v_wjcgesLOSz8,25543
sympy/printing/precedence.py,sha256=zuP8UnTSmSDTfVBWOJSL7pOF72oB5-OoACyxkceTiWQ,5233
sympy/printing/pretty/__init__.py,sha256=pJTe-DO4ctTlnjg1UvqyoeBY50B5znFjcGvivXRhM2U,344
sympy/printing/pretty/__pycache__/__init__.cpython-310.pyc,,
sympy/printing/pretty/__pycache__/pretty.cpython-310.pyc,,
sympy/printing/pretty/__pycache__/pretty_symbology.cpython-310.pyc,,
sympy/printing/pretty/__pycache__/stringpict.cpython-310.pyc,,
sympy/printing/pretty/pretty.py,sha256=KmEXRb6XEtI5tiQYssueibKKrJVF1F1mwrF1uHGxmpM,105024
sympy/printing/pretty/pretty_symbology.py,sha256=Gc38sHj10LsKkLbJ7fCPHjQt9mmiGCkblHtDvw8QvRU,24122
sympy/printing/pretty/stringpict.py,sha256=UbG55kC4ve8-Dby_v0cm8AXUdLcu4YDLXVnA8F_iV8Y,19158
sympy/printing/pretty/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/printing/pretty/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/printing/pretty/tests/__pycache__/test_pretty.cpython-310.pyc,,
sympy/printing/pretty/tests/test_pretty.py,sha256=K2Xt--ptdxWLuUmtYJnuyh81ZrGIDRqNM_iOUADAdjA,187716
sympy/printing/preview.py,sha256=FwN0_q52iU6idLNZNXo002gPNpVw_9xrxLifFnK_ssw,14104
sympy/printing/printer.py,sha256=0-hGTS9IPEqqP3s2sW7cZWyBe6opGa1FzyIRhND6FkA,14479
sympy/printing/pycode.py,sha256=_D3u7jtPeXzwGgn-aTKzB81JDi8Qzy9u4vqATY-DSdE,24878
sympy/printing/python.py,sha256=oxGJhNQUXRKjHSEgqroGAyB-A4ZgJQqA_o7C8uXA1U8,3347
sympy/printing/rcode.py,sha256=yQtoOuffTzEAV58d7NLGhSca78wiwZESeFb90H7ZAj8,14173
sympy/printing/repr.py,sha256=_pyPmyMMlh0bJ-PmcWXJrWcy-TDXwsOOfDMdTKFIE58,11520
sympy/printing/rust.py,sha256=GZXuOPhf5L9nWet79RBoXY36n9J2umVj7BrjiNotxa4,21243
sympy/printing/smtlib.py,sha256=MBZbz8kmb9rymdmSioMGi1f0VXaiXnM6uxRPWErxNpA,21743
sympy/printing/str.py,sha256=N22a-U4Zbru5QtAYGiEYRL_gC9I3kXTnPUE2XfF7vXI,33218
sympy/printing/tableform.py,sha256=pz4hXDLZ1IlqU8eOo0XBX1xaALYMqe4qHPUnEVq0mAE,11797
sympy/printing/tensorflow.py,sha256=KHdJMHMBOaJkHO8_uBfYRHeBW2VIziv_YYqIV30D-dA,7906
sympy/printing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/printing/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_aesaracode.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_c.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_codeprinter.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_conventions.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_cupy.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_cxx.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_dot.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_fortran.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_glsl.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_gtk.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_jax.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_jscode.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_julia.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_lambdarepr.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_latex.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_llvmjit.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_maple.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_mathematica.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_mathml.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_numpy.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_octave.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_precedence.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_preview.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_pycode.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_python.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_rcode.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_repr.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_rust.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_smtlib.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_str.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_tableform.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_tensorflow.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_theanocode.cpython-310.pyc,,
sympy/printing/tests/__pycache__/test_tree.cpython-310.pyc,,
sympy/printing/tests/test_aesaracode.py,sha256=tLT62BKCUSlkU1_4cGwgEYARsEEnL6RzY8Gv-Hwu6pI,21023
sympy/printing/tests/test_c.py,sha256=46BUfBuL_48Up9OntBHgc0k1c__VLDiQ4Ki4aBqiKxI,31045
sympy/printing/tests/test_codeprinter.py,sha256=GDtgFMRMRGQutLJNxsQCdl0-zb4b4mOQmXqJ-NswKgY,1464
sympy/printing/tests/test_conventions.py,sha256=yqPpU3F0WcbxImPBBAHd3YEZpkFGfcq_TLK4WN_gtP4,5257
sympy/printing/tests/test_cupy.py,sha256=E2T4a82XITbWAssmRZD4zCQdMCrpaZFCAjGXthUp8cY,1882
sympy/printing/tests/test_cxx.py,sha256=0XQPQdtDCgEKVfpuelEB-79f7d2fe9QPgEKCCYVAI_w,3183
sympy/printing/tests/test_dot.py,sha256=TSAtgGIgK_JbY-RMbQgUvnAI87SJqeJOqzcLjAobhKM,4648
sympy/printing/tests/test_fortran.py,sha256=d5uy1zp0IgaBPupzXpNuk169YZYygnodPWhNRcvUtAQ,35456
sympy/printing/tests/test_glsl.py,sha256=cfog9fp_EOFm_piJwqUcSvAIJ78bRwkFjecwr3ocCak,28421
sympy/printing/tests/test_gtk.py,sha256=94gp1xRlPrFiALQGuqHnmh9xKrMxR52RQVkN0MXbUdA,500
sympy/printing/tests/test_jax.py,sha256=KI636AeyZoV1luc3gBKHVyxSvLv1hnYQgPNneSiM2-o,11062
sympy/printing/tests/test_jscode.py,sha256=ObahZne9lQbBiXyJZLohjQGdHsG2CnWCFOB8KbFOAqQ,11369
sympy/printing/tests/test_julia.py,sha256=5I3HepnkEmtlwPZ-tKqEGzPTFTQJtQ-eYTBQLJ0Kntk,13832
sympy/printing/tests/test_lambdarepr.py,sha256=YU_lAQpiNHKJpBjZmgXr-unzOwS6Ss-u8sS2D_u-Mq0,6947
sympy/printing/tests/test_latex.py,sha256=HjqpedlvRrVS1uV6LEnv1MuoXkXMnbA_6_Fr-kTuh7g,136938
sympy/printing/tests/test_llvmjit.py,sha256=EGPeRisM60_TIVgnk7PTLSm5F-Aod_88zLjHPZwfyZ8,5344
sympy/printing/tests/test_maple.py,sha256=dhcz2bqapB_qMyBLMgsgnvVRIcHQ-HmxvAxC_Y0Z3q8,13078
sympy/printing/tests/test_mathematica.py,sha256=vijg7xfoelywL-ZhNuXFfDjM1FgaW_4liTBx1wzpkWk,10954
sympy/printing/tests/test_mathml.py,sha256=h8m0Qd7ix14eUOtH8AHQArr__Q7oCwY_P_n_oo8WR4U,96056
sympy/printing/tests/test_numpy.py,sha256=XfbTPHu98aAuLqZ7P2brn4rTcogG0lLRy4YUIBpX_0s,10907
sympy/printing/tests/test_octave.py,sha256=RVXgqAVKrI6ec4I0QzUY5vRi2oDW-e4QmggYRitiFhM,18553
sympy/printing/tests/test_precedence.py,sha256=CS4L-WbI2ZuWLgbGATtF41--h0iGkfuE6dK5DYYiC5g,2787
sympy/printing/tests/test_preview.py,sha256=dSVxiGqdNR6gbF40V4J2tGhQ-T4RDvSyGypHvYcPDYM,988
sympy/printing/tests/test_pycode.py,sha256=Gx_AULo7o5wL40CMVlqrV82DDuR6PnMI5vIRp87EYCw,16070
sympy/printing/tests/test_python.py,sha256=HN7JkzQcKSnB6718i7kaEJZ5pYMqu56z1mSmHQGzY4k,8128
sympy/printing/tests/test_rcode.py,sha256=TXyl449eCO4J-P3DQb3w9FWvMqOdXmfuiXgyBg_inQQ,13781
sympy/printing/tests/test_repr.py,sha256=5XmDdIDlQlCWckWq8H95Fw82h-oDxrRpMWZePb6hHa4,12980
sympy/printing/tests/test_rust.py,sha256=6WjwI9ItuTPWX9LmR1gupIXTmspXUUjjeSZNopDxnG0,11512
sympy/printing/tests/test_smtlib.py,sha256=3r7jKnoXQiPaUMV3nDYSUW9yArRdQRLGlQZh5T1I0Io,22526
sympy/printing/tests/test_str.py,sha256=UMo5ytE7mnt7Y3J1U002u4HPX2wF42Kf-UGio7XrxFM,43129
sympy/printing/tests/test_tableform.py,sha256=Ff5l1QL2HxN32WS_TdFhUAVqzop8YoWY3Uz1TThvVIM,5692
sympy/printing/tests/test_tensorflow.py,sha256=a1SMaYgZVTAuunaOs3iwASx2i0V1DbBYMIUWEpYqphc,15578
sympy/printing/tests/test_theanocode.py,sha256=E36Fj72HxMK0e1pKTkoTpv9wI4UvwHdVufo-JA6dYq0,21394
sympy/printing/tests/test_tree.py,sha256=_8PGAhWMQ_A0f2DQLdDeMrpxY19889P5Ih9H41RZn8s,6080
sympy/printing/theanocode.py,sha256=sMN5vGZfS_hfPq5Dm85rkhaCLa-lTYNyWhU5oDa3r0o,19092
sympy/printing/tree.py,sha256=GxEF1WIflPNShlOrZc8AZch2I6GxDlbpImHqX61_P5o,3872
sympy/release.py,sha256=KEnQD-7zBoKmB5YS3RYdSTAm0jGS_-WENjL881LrJOY,23
sympy/sandbox/__init__.py,sha256=IaEVOYHaZ97OHEuto1UGthFuO35c0uvAZFZU23YyEaU,189
sympy/sandbox/__pycache__/__init__.cpython-310.pyc,,
sympy/sandbox/__pycache__/indexed_integrals.cpython-310.pyc,,
sympy/sandbox/indexed_integrals.py,sha256=svh4xDIa8nGpDeH4TeRb49gG8miMvXpCzEarbor58EE,2141
sympy/sandbox/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/sandbox/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/sandbox/tests/__pycache__/test_indexed_integrals.cpython-310.pyc,,
sympy/sandbox/tests/test_indexed_integrals.py,sha256=UK2E2wg9EMwda4Vwpzyj3rmXs6ni33HqcbyaqAww6ww,1179
sympy/series/__init__.py,sha256=DYG9oisjzYeS55dIUpQpbAFcoDz7Q81fZJw36PRGu14,766
sympy/series/__pycache__/__init__.cpython-310.pyc,,
sympy/series/__pycache__/acceleration.cpython-310.pyc,,
sympy/series/__pycache__/approximants.cpython-310.pyc,,
sympy/series/__pycache__/aseries.cpython-310.pyc,,
sympy/series/__pycache__/formal.cpython-310.pyc,,
sympy/series/__pycache__/fourier.cpython-310.pyc,,
sympy/series/__pycache__/gruntz.cpython-310.pyc,,
sympy/series/__pycache__/kauers.cpython-310.pyc,,
sympy/series/__pycache__/limits.cpython-310.pyc,,
sympy/series/__pycache__/limitseq.cpython-310.pyc,,
sympy/series/__pycache__/order.cpython-310.pyc,,
sympy/series/__pycache__/residues.cpython-310.pyc,,
sympy/series/__pycache__/sequences.cpython-310.pyc,,
sympy/series/__pycache__/series.cpython-310.pyc,,
sympy/series/__pycache__/series_class.cpython-310.pyc,,
sympy/series/acceleration.py,sha256=9VTCOEOgIyOvcwjY5ZT_c4kWE-f_bL79iz_T3WGis94,3357
sympy/series/approximants.py,sha256=tE-hHuoW62QJHDA3WhRlXaTkokCAODs1vXgjirhOYiQ,3181
sympy/series/aseries.py,sha256=cHVGRQaza4ayqI6ji6OHNkdQEMV7Bko4f4vug2buEQY,255
sympy/series/benchmarks/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/series/benchmarks/__pycache__/__init__.cpython-310.pyc,,
sympy/series/benchmarks/__pycache__/bench_limit.cpython-310.pyc,,
sympy/series/benchmarks/__pycache__/bench_order.cpython-310.pyc,,
sympy/series/benchmarks/bench_limit.py,sha256=2PtdeeJtD6qyEvt9HFNvyTnMM8phFZRjscgnb4fHndU,173
sympy/series/benchmarks/bench_order.py,sha256=iC8sQJ0lLlTgiXltAyLzSCQ-3490cf-c6NFiIU44JSk,207
sympy/series/formal.py,sha256=CtRziTUItAd8G9z__jJ9s7dRIHAOdeHajdPmNB3HRgY,51772
sympy/series/fourier.py,sha256=Pmpqy-Ts_FRGVMP31tJXg42cDSX7hqV0NmkVSKmu1-0,22948
sympy/series/gruntz.py,sha256=5kTeEQHPukYr5skPCeffGhICpDLnYRPAiQOQN-XMYP4,24599
sympy/series/kauers.py,sha256=PzD0MATMNjLjPi9GW5GQGL6Uqc2UT-uPwnzhi7TkJH8,1720
sympy/series/limits.py,sha256=E_sYyi_SPH4V60K28RUMd2gZY-VmVKxcwsQFtAATOPQ,12843
sympy/series/limitseq.py,sha256=WM1Lh3RXhSZM1gQaJrhWnUtYEgJunLujIEw1gmtVhYw,7752
sympy/series/order.py,sha256=bKvLPG0QwPl3a7Qw-SMQEjkpyaTxxye7pvC27-jvt80,19255
sympy/series/residues.py,sha256=k46s_fFfIHdJZqfst-B_-X1R-SAWs_rR9MQH7a9JLtg,2213
sympy/series/sequences.py,sha256=WLubAZr5AevpllgI4b6WmIfXRX88QNVCpMGBofGkcqo,35543
sympy/series/series.py,sha256=PveJ6LibsrDX-mymTbV7teNggnsX0cfaoGMLAP0KAIo,1863
sympy/series/series_class.py,sha256=033NJ5Re8AS4eq-chmfct3-Lz2vBqdFqXtnrbxswTx0,2918
sympy/series/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/series/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/series/tests/__pycache__/test_approximants.cpython-310.pyc,,
sympy/series/tests/__pycache__/test_aseries.cpython-310.pyc,,
sympy/series/tests/__pycache__/test_demidovich.cpython-310.pyc,,
sympy/series/tests/__pycache__/test_formal.cpython-310.pyc,,
sympy/series/tests/__pycache__/test_fourier.cpython-310.pyc,,
sympy/series/tests/__pycache__/test_gruntz.cpython-310.pyc,,
sympy/series/tests/__pycache__/test_kauers.cpython-310.pyc,,
sympy/series/tests/__pycache__/test_limits.cpython-310.pyc,,
sympy/series/tests/__pycache__/test_limitseq.cpython-310.pyc,,
sympy/series/tests/__pycache__/test_lseries.cpython-310.pyc,,
sympy/series/tests/__pycache__/test_nseries.cpython-310.pyc,,
sympy/series/tests/__pycache__/test_order.cpython-310.pyc,,
sympy/series/tests/__pycache__/test_residues.cpython-310.pyc,,
sympy/series/tests/__pycache__/test_sequences.cpython-310.pyc,,
sympy/series/tests/__pycache__/test_series.cpython-310.pyc,,
sympy/series/tests/test_approximants.py,sha256=KViHMW1dPXn7xaPYhtTQ9L_WtLLkoIic6yfFnwZ8Q70,1012
sympy/series/tests/test_aseries.py,sha256=LblW4hBDVhigX9YvNc_HFvMm8nJMSTAT9PcUK3p-9HU,2371
sympy/series/tests/test_demidovich.py,sha256=JGYacqJMEqHS6oT2AYs9d7iutIEb32PkJs9EJqOHxcQ,4947
sympy/series/tests/test_formal.py,sha256=k2rqySJg6WnPSwcDyQBG7041bJxXdiYZt-KSs_IAso0,22495
sympy/series/tests/test_fourier.py,sha256=Dknk64RWGNO8kXmpy2RRIbT8b-0CjL_35QcBugReW38,5891
sympy/series/tests/test_gruntz.py,sha256=p9hTN739E8Fw-MaBTH0Z_C8olGUOlQY8fLqDvcHufHw,16215
sympy/series/tests/test_kauers.py,sha256=Z85FhfXOOVki0HNGeK5BEBZOpkuB6SnKK3FqfK1-aLQ,1102
sympy/series/tests/test_limits.py,sha256=2E0-Crcy5Jo-9xDNQJ_VAWS9pwm-QGiBNO4ra8loWvw,47711
sympy/series/tests/test_limitseq.py,sha256=QjEF99sYEDqfY7ULz1qjQTo6e0lIRUCflEOBgiDYRVA,5691
sympy/series/tests/test_lseries.py,sha256=GlQvlBlD9wh02PPBP6zU83wmhurvGUFTuCRp44B4uI4,1875
sympy/series/tests/test_nseries.py,sha256=uzhzYswSOe9Gh_nWKeO69tvGPMLd-9tqk4HBYX8JIm4,18284
sympy/series/tests/test_order.py,sha256=BGB1j0vmSMS8lGwSVmBOc9apI1NM82quFwF2Hhr2bDE,16500
sympy/series/tests/test_residues.py,sha256=pT9xzPqtmfKGSbLLAxgDVZLTSy3TOxyfq3thTJs2VLw,3178
sympy/series/tests/test_sequences.py,sha256=Oyq32yQZnGNQDS2uJ3by3bZ-y4G9c9BFfdQTcVuW2RM,11161
sympy/series/tests/test_series.py,sha256=YKpUhV_D2-dqsrKaIlREMR2NskG0TB-Gxb3S0xIIdzo,16458
sympy/sets/__init__.py,sha256=3vjCm4v2esbpsVPY0ROwTXMETxns_66bG4FCIFZ96oM,1026
sympy/sets/__pycache__/__init__.cpython-310.pyc,,
sympy/sets/__pycache__/conditionset.cpython-310.pyc,,
sympy/sets/__pycache__/contains.cpython-310.pyc,,
sympy/sets/__pycache__/fancysets.cpython-310.pyc,,
sympy/sets/__pycache__/ordinals.cpython-310.pyc,,
sympy/sets/__pycache__/powerset.cpython-310.pyc,,
sympy/sets/__pycache__/setexpr.cpython-310.pyc,,
sympy/sets/__pycache__/sets.cpython-310.pyc,,
sympy/sets/conditionset.py,sha256=mhodBVrMqJ6W5H8CuaFhO3FO9VdJifOEPjjFmV9lT2I,7792
sympy/sets/contains.py,sha256=AlsfOc_6V0TH_6G7XTPIXhDywgtg3ECdjCTfSOmPC-E,1829
sympy/sets/fancysets.py,sha256=G2W3wACszK7rq4QhhIPyDl3yhCBOtnT5FN3o03gza7Y,48169
sympy/sets/handlers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/sets/handlers/__pycache__/__init__.cpython-310.pyc,,
sympy/sets/handlers/__pycache__/add.cpython-310.pyc,,
sympy/sets/handlers/__pycache__/comparison.cpython-310.pyc,,
sympy/sets/handlers/__pycache__/functions.cpython-310.pyc,,
sympy/sets/handlers/__pycache__/intersection.cpython-310.pyc,,
sympy/sets/handlers/__pycache__/issubset.cpython-310.pyc,,
sympy/sets/handlers/__pycache__/mul.cpython-310.pyc,,
sympy/sets/handlers/__pycache__/power.cpython-310.pyc,,
sympy/sets/handlers/__pycache__/union.cpython-310.pyc,,
sympy/sets/handlers/add.py,sha256=_ucFvxuDv9wsmKxGkCDUERtYk3I_tQxjZjY3ZkroWs0,1863
sympy/sets/handlers/comparison.py,sha256=WfT_vLrOkvPqRg2mf7gziVs_6cLg0kOTEFv-Nb1zIvo,1601
sympy/sets/handlers/functions.py,sha256=jYSFqFNH6mXbKFPgvIAIGY8BhbLPo1dAvcNg4MxmCaI,8381
sympy/sets/handlers/intersection.py,sha256=oYPmrx3FAkGLVWT3EjSimeMfsGqPsqVnpJm5UxQzuCM,17514
sympy/sets/handlers/issubset.py,sha256=azka_5eOaUro3r3v72PmET0oY8-aaoJkzVEK7kuqXCA,4739
sympy/sets/handlers/mul.py,sha256=XFbkOw4PDQumaOEUlHeQLvjhIom0f3iniSYv_Kau-xw,1842
sympy/sets/handlers/power.py,sha256=84N3dIus7r09XV7PF_RiEpFRw1y5tOGD34WKzSM9F-4,3186
sympy/sets/handlers/union.py,sha256=lrAdydqExnALUjM0dnoM-7JAZqtbgLb46Y2GGmFtQdw,4225
sympy/sets/ordinals.py,sha256=GSyaBq7BHJC3pvgoCDoUKZQ0IE2VXyHtx6_g5OS64W4,7641
sympy/sets/powerset.py,sha256=vIGnSYKngEPEt6V-6beDOXAOY9ugDLJ8fXOx5H9JJck,2913
sympy/sets/setexpr.py,sha256=jMOQigDscLTrFPXvHqo1ODVRG9BqC4yn38Ej4m6WPa0,3019
sympy/sets/sets.py,sha256=IrpowoWh2S667x1dpN9dfRUJtrlm8ngCu-Z2fiwuDPY,79678
sympy/sets/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/sets/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/sets/tests/__pycache__/test_conditionset.cpython-310.pyc,,
sympy/sets/tests/__pycache__/test_contains.cpython-310.pyc,,
sympy/sets/tests/__pycache__/test_fancysets.cpython-310.pyc,,
sympy/sets/tests/__pycache__/test_ordinals.cpython-310.pyc,,
sympy/sets/tests/__pycache__/test_powerset.cpython-310.pyc,,
sympy/sets/tests/__pycache__/test_setexpr.cpython-310.pyc,,
sympy/sets/tests/__pycache__/test_sets.cpython-310.pyc,,
sympy/sets/tests/test_conditionset.py,sha256=UeutWuBCT5PZicNkkl9E94pREnU5CvLGWUfmRS29PcU,11370
sympy/sets/tests/test_contains.py,sha256=ec6WRzriwV9nurz3jS9IXEqtfL1pZedtJFp--fSsBvY,1561
sympy/sets/tests/test_fancysets.py,sha256=oiuu5PKNrDeDO-NtgsE4CpkwQaS7JNgCYuSyc1ykKKE,51938
sympy/sets/tests/test_ordinals.py,sha256=L4DYc6ByQMDwJGFzJC3YhfSrVk5auW7pf4QYpJ5xY7w,2637
sympy/sets/tests/test_powerset.py,sha256=nFvDGlhAf0wG-pZnPkgJjfwDHrTwdro3MYIinwyxn94,4805
sympy/sets/tests/test_setexpr.py,sha256=E--SjYVzrmau0EbD8g4NTqp6aLD8qHzIuI7sAfuWxpY,14797
sympy/sets/tests/test_sets.py,sha256=oMtqcr9gr68itfWH4j18POID8tD0hAZUV8HCz7-KKj0,68544
sympy/simplify/__init__.py,sha256=MH1vkwHq0J5tNm7ss8V6v-mjrDGUXwfOsariIwfi38c,1274
sympy/simplify/__pycache__/__init__.cpython-310.pyc,,
sympy/simplify/__pycache__/combsimp.cpython-310.pyc,,
sympy/simplify/__pycache__/cse_main.cpython-310.pyc,,
sympy/simplify/__pycache__/cse_opts.cpython-310.pyc,,
sympy/simplify/__pycache__/epathtools.cpython-310.pyc,,
sympy/simplify/__pycache__/fu.cpython-310.pyc,,
sympy/simplify/__pycache__/gammasimp.cpython-310.pyc,,
sympy/simplify/__pycache__/hyperexpand.cpython-310.pyc,,
sympy/simplify/__pycache__/hyperexpand_doc.cpython-310.pyc,,
sympy/simplify/__pycache__/powsimp.cpython-310.pyc,,
sympy/simplify/__pycache__/radsimp.cpython-310.pyc,,
sympy/simplify/__pycache__/ratsimp.cpython-310.pyc,,
sympy/simplify/__pycache__/simplify.cpython-310.pyc,,
sympy/simplify/__pycache__/sqrtdenest.cpython-310.pyc,,
sympy/simplify/__pycache__/traversaltools.cpython-310.pyc,,
sympy/simplify/__pycache__/trigsimp.cpython-310.pyc,,
sympy/simplify/combsimp.py,sha256=XZOyP8qxowsXNbrtdUiinUFTUau4DZvivmd--Cw8Jnk,3605
sympy/simplify/cse_main.py,sha256=hDTZTwejxspK8zw-2LXZHF9rL_FMrK3byYoIsA_YroI,31342
sympy/simplify/cse_opts.py,sha256=ZTCaOdOrgtifWxQmFzyngrLq9uwzByBdiSS5mE-DDoE,1618
sympy/simplify/epathtools.py,sha256=YEeS5amYseT1nC4bHqyyemrjAE1qlhWz0ISXJk5I8Xo,10173
sympy/simplify/fu.py,sha256=tkuPWIwrfGQZ67hQ1Z3Z1aaFH-6Fku9E474W0GNP3-k,62310
sympy/simplify/gammasimp.py,sha256=n-TDIl7W_8RPSvpRTk8XiRSvYDBpzh55xxxWBpdXrfI,18609
sympy/simplify/hyperexpand.py,sha256=fVqhG478gpHQXowrFAjs3by7cfrkbev-ry2cP_bs828,84421
sympy/simplify/hyperexpand_doc.py,sha256=E8AD0mj8ULtelDSUkmJKJY7kYm5fVfCL4QH_DX65qEw,521
sympy/simplify/powsimp.py,sha256=iQD19_nZBOn4n-xCICkNV1-Wqm7Xb2LDvDYdWIWTh50,26575
sympy/simplify/radsimp.py,sha256=fV0aN3zsHS-wdTeoCbKLekhtOxdIeMvkWdxjtNQHCN0,41323
sympy/simplify/ratsimp.py,sha256=vTu0t0k1FDFTofl4mwcK9NuTMNZ20IsL8fIyFJRFNkg,7682
sympy/simplify/simplify.py,sha256=e6I50Pjy_I3ENOCpAkwWq7cttySpVSYzP5_ggkDowAE,72925
sympy/simplify/sqrtdenest.py,sha256=nrQjZ4MeWpzxJx-YjTR2rUg9sOj4K9SJxxY5K4Px-gM,21600
sympy/simplify/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/simplify/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/simplify/tests/__pycache__/test_combsimp.cpython-310.pyc,,
sympy/simplify/tests/__pycache__/test_cse.cpython-310.pyc,,
sympy/simplify/tests/__pycache__/test_epathtools.cpython-310.pyc,,
sympy/simplify/tests/__pycache__/test_fu.cpython-310.pyc,,
sympy/simplify/tests/__pycache__/test_function.cpython-310.pyc,,
sympy/simplify/tests/__pycache__/test_gammasimp.cpython-310.pyc,,
sympy/simplify/tests/__pycache__/test_hyperexpand.cpython-310.pyc,,
sympy/simplify/tests/__pycache__/test_powsimp.cpython-310.pyc,,
sympy/simplify/tests/__pycache__/test_radsimp.cpython-310.pyc,,
sympy/simplify/tests/__pycache__/test_ratsimp.cpython-310.pyc,,
sympy/simplify/tests/__pycache__/test_rewrite.cpython-310.pyc,,
sympy/simplify/tests/__pycache__/test_simplify.cpython-310.pyc,,
sympy/simplify/tests/__pycache__/test_sqrtdenest.cpython-310.pyc,,
sympy/simplify/tests/__pycache__/test_trigsimp.cpython-310.pyc,,
sympy/simplify/tests/test_combsimp.py,sha256=O95WSxCvo2fDQs-UlarAcSf0_8M3PuTR76lhREDoNA8,2958
sympy/simplify/tests/test_cse.py,sha256=sas6-jRlHaZ6jdiXoGTC52BNZvuUtaYptIQ1P-5JO00,25513
sympy/simplify/tests/test_epathtools.py,sha256=ugsQlfuK6POiixdeit63QovsVAlG5JyCaPlPp0j35LE,3525
sympy/simplify/tests/test_fu.py,sha256=X2NNsynwefg2aH5GhyxQItL80fKPZ9md6nydMEedWbQ,19410
sympy/simplify/tests/test_function.py,sha256=gzdcSFObuDzVFJDdAgmERtZJvG38WNSmclPAdG8OaPQ,2199
sympy/simplify/tests/test_gammasimp.py,sha256=32cPRmtG-_Mz9g02lmmn-PWDD3J_Ku6sxLxIUU7WqxE,5320
sympy/simplify/tests/test_hyperexpand.py,sha256=y3lxd97UZ1BkrrZe0r2l6MM8zNABTE_L4KtxFwmdzeQ,41043
sympy/simplify/tests/test_powsimp.py,sha256=CG5H_xSbtwZakjLzL-EEg-T9j2GOUylCU5YgLsbHm2A,14313
sympy/simplify/tests/test_radsimp.py,sha256=7GjCVKP_nyS8s36Oxwmw6TiPRY0fG3aZP9Rd3oSksTY,18789
sympy/simplify/tests/test_ratsimp.py,sha256=bv-K60A7m2on-U9szzaeYO7Hlp1EK5P0HvBBO59oyas,2198
sympy/simplify/tests/test_rewrite.py,sha256=LZj4V6a95GJj1o3NlKRoHMk7sWGPASFlw24nsm4z43k,1127
sympy/simplify/tests/test_simplify.py,sha256=xI7bi4eRupmVeadi6ykHSGZQJbZdf6E_xo8qdjOVfiU,41735
sympy/simplify/tests/test_sqrtdenest.py,sha256=4zRtDQVGpKRRBYSAnEF5pSM0AR_fAMumONu2Ocb3tqg,7470
sympy/simplify/tests/test_trigsimp.py,sha256=vG5PDTDNOuFypT7H9DSMjIollPqkKdNhWv5FBj6vFnE,19949
sympy/simplify/traversaltools.py,sha256=pn_t9Yrk_SL1X0vl-zVR6yZaxkY25D4MwTBv4ywnD1Y,409
sympy/simplify/trigsimp.py,sha256=dB5MjduBTBj-htI85z58jOS2paRSnOuGPdpRZ6ILRew,46878
sympy/solvers/__init__.py,sha256=qNPrpG5Q45t6jgIw1xpGwcH4nvsr7LpMfR83X88kgFw,2276
sympy/solvers/__pycache__/__init__.cpython-310.pyc,,
sympy/solvers/__pycache__/bivariate.cpython-310.pyc,,
sympy/solvers/__pycache__/decompogen.cpython-310.pyc,,
sympy/solvers/__pycache__/deutils.cpython-310.pyc,,
sympy/solvers/__pycache__/inequalities.cpython-310.pyc,,
sympy/solvers/__pycache__/pde.cpython-310.pyc,,
sympy/solvers/__pycache__/polysys.cpython-310.pyc,,
sympy/solvers/__pycache__/recurr.cpython-310.pyc,,
sympy/solvers/__pycache__/simplex.cpython-310.pyc,,
sympy/solvers/__pycache__/solvers.cpython-310.pyc,,
sympy/solvers/__pycache__/solveset.cpython-310.pyc,,
sympy/solvers/benchmarks/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/solvers/benchmarks/__pycache__/__init__.cpython-310.pyc,,
sympy/solvers/benchmarks/__pycache__/bench_solvers.cpython-310.pyc,,
sympy/solvers/benchmarks/bench_solvers.py,sha256=ZVK2TIW0XjWRDBex054ymmVlSBQw-RIBhEL1wS2ZAmU,288
sympy/solvers/bivariate.py,sha256=yrlo0AoY_MtXHP1j0qKV4UgAhSXBBpvHHRnDJuCFsC8,17869
sympy/solvers/decompogen.py,sha256=dWQla7hp7A4RqI2a0qRNQLWNPEuur68lD3dVTyktdBU,3757
sympy/solvers/deutils.py,sha256=6dCIoZqX8mFz77SpT1DOM_I5yvdwU1tUMnTbA2vjYME,10309
sympy/solvers/diophantine/__init__.py,sha256=I1p3uj3kFQv20cbsZ34K5rNCx1_pDS7JwHUCFstpBgs,128
sympy/solvers/diophantine/__pycache__/__init__.cpython-310.pyc,,
sympy/solvers/diophantine/__pycache__/diophantine.cpython-310.pyc,,
sympy/solvers/diophantine/diophantine.py,sha256=jHIV6Ol8fTgxaQl0Gs4JZrU9sLoZXdNXRGBjeWJmbAw,121871
sympy/solvers/diophantine/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/solvers/diophantine/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/solvers/diophantine/tests/__pycache__/test_diophantine.cpython-310.pyc,,
sympy/solvers/diophantine/tests/test_diophantine.py,sha256=dAoMZU18ViYo5nUTCkdCEBK5aBX09jsFm6xRXSeVGLw,42691
sympy/solvers/inequalities.py,sha256=qZPlOg3f4iRc54zjkn93aE1VLTQvuWqo75IV9UrWCmg,33202
sympy/solvers/ode/__init__.py,sha256=I7RKwCcaoerflUm5i3ZDJgBIOnkhBjb83BCHcVcFqfM,468
sympy/solvers/ode/__pycache__/__init__.cpython-310.pyc,,
sympy/solvers/ode/__pycache__/hypergeometric.cpython-310.pyc,,
sympy/solvers/ode/__pycache__/lie_group.cpython-310.pyc,,
sympy/solvers/ode/__pycache__/nonhomogeneous.cpython-310.pyc,,
sympy/solvers/ode/__pycache__/ode.cpython-310.pyc,,
sympy/solvers/ode/__pycache__/riccati.cpython-310.pyc,,
sympy/solvers/ode/__pycache__/single.cpython-310.pyc,,
sympy/solvers/ode/__pycache__/subscheck.cpython-310.pyc,,
sympy/solvers/ode/__pycache__/systems.cpython-310.pyc,,
sympy/solvers/ode/hypergeometric.py,sha256=kizvLgjzX1VUZ1n84uT6tlOs_8NfQBW1JZVo0fJLkdM,10048
sympy/solvers/ode/lie_group.py,sha256=7RoBlKEBNedU4GbDm41tbWDMdOeECfz3EoMDzKV8BnE,39196
sympy/solvers/ode/nonhomogeneous.py,sha256=SyQVXK3BB1gEZlcK1q5LueWvpyo-U600tdnpV_87QbE,18231
sympy/solvers/ode/ode.py,sha256=7iqc9mWFTUWqgSx4ApBs-gmZNaLn47bQ-eWnRroTA_0,145487
sympy/solvers/ode/riccati.py,sha256=3ZrkCy3ufXCyCx8gJfAuxyCOgbOLEHtvTh2VgJhA7Mk,30736
sympy/solvers/ode/single.py,sha256=LwwCXg2yRgDhAR0AxD60OOWJ8o3rpn-iRja20Q-xmqE,109476
sympy/solvers/ode/subscheck.py,sha256=CIPca_qTxL9z5oaD2e2NrgME0eVQgF9PabZndcVqHZM,16130
sympy/solvers/ode/systems.py,sha256=Kok2AMO4YEKX38fi2LrxLm5zpTcNXz398b7OBvrUezU,71467
sympy/solvers/ode/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/solvers/ode/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/solvers/ode/tests/__pycache__/test_lie_group.cpython-310.pyc,,
sympy/solvers/ode/tests/__pycache__/test_ode.cpython-310.pyc,,
sympy/solvers/ode/tests/__pycache__/test_riccati.cpython-310.pyc,,
sympy/solvers/ode/tests/__pycache__/test_single.cpython-310.pyc,,
sympy/solvers/ode/tests/__pycache__/test_subscheck.cpython-310.pyc,,
sympy/solvers/ode/tests/__pycache__/test_systems.cpython-310.pyc,,
sympy/solvers/ode/tests/test_lie_group.py,sha256=vg1yy_-a5x1Xm2IcVkEi5cD2uA5wE5gjqpfBwkV1vZc,5319
sympy/solvers/ode/tests/test_ode.py,sha256=AoKk0b66J3O37cy1bb5F9j5252YmFGTWghXdZI8xFH4,48508
sympy/solvers/ode/tests/test_riccati.py,sha256=a2_pXzCFb9WDtT_kdxYkfLd-PG5xvs4rQn_vpFk-O9s,29348
sympy/solvers/ode/tests/test_single.py,sha256=AwtXi4jtAonvYFRegM7th10b68wVdFPPBwyGVrrT9Pg,100199
sympy/solvers/ode/tests/test_subscheck.py,sha256=Gzwc9h9n6zlNOhJ8Qh6fQDeB8ghaRmgv3ktBAfPJx-U,12468
sympy/solvers/ode/tests/test_systems.py,sha256=PeIEHOx8-eZQNuqOfhjTvGEeFSVriLlHShPmy84mde4,129087
sympy/solvers/pde.py,sha256=hEEy_67y8FrWotMbjoAYL1nPBPumLSaEJcE3mx-7s-M,35023
sympy/solvers/polysys.py,sha256=HaI0OxKF0iNwK9XsTz8S1dzUO9yMbWqRGIsv3avrSAM,13169
sympy/solvers/recurr.py,sha256=DyssZuOyemoC6J1cWq635O7zkg1WLHrR7KGoM-gNy0g,25389
sympy/solvers/simplex.py,sha256=qB6r8HqCNBbJtqdXGW5UHLsUXXF100mKq-fraM4xAqk,35731
sympy/solvers/solvers.py,sha256=LvfHjd15yFNOv0l5z9CApkHJ4-kUWLd85TDXXvaCtUc,138344
sympy/solvers/solveset.py,sha256=2KvYSRePbkPPb050xo2puBtI0BjARFAS71qz46igj-0,151783
sympy/solvers/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/solvers/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/solvers/tests/__pycache__/test_constantsimp.cpython-310.pyc,,
sympy/solvers/tests/__pycache__/test_decompogen.cpython-310.pyc,,
sympy/solvers/tests/__pycache__/test_inequalities.cpython-310.pyc,,
sympy/solvers/tests/__pycache__/test_numeric.cpython-310.pyc,,
sympy/solvers/tests/__pycache__/test_pde.cpython-310.pyc,,
sympy/solvers/tests/__pycache__/test_polysys.cpython-310.pyc,,
sympy/solvers/tests/__pycache__/test_recurr.cpython-310.pyc,,
sympy/solvers/tests/__pycache__/test_simplex.cpython-310.pyc,,
sympy/solvers/tests/__pycache__/test_solvers.cpython-310.pyc,,
sympy/solvers/tests/__pycache__/test_solveset.cpython-310.pyc,,
sympy/solvers/tests/test_constantsimp.py,sha256=9Feugsg9jD2BwQiG4EFpb9fORyst6JdBmZqq2GaOgH8,8707
sympy/solvers/tests/test_decompogen.py,sha256=7GUsDQQZtYbZIK0p0UxsOuNEJxEt4IHeOSsem_k-k0U,2943
sympy/solvers/tests/test_inequalities.py,sha256=whg3vGXEYxeIHNQS4yBeB9VQpoYWnfw5NBS4xLiqDJ8,21025
sympy/solvers/tests/test_numeric.py,sha256=pKLBJuf4lGCewf5DgBjvH5T9phf6ilkyW1gB2R-5SzA,4734
sympy/solvers/tests/test_pde.py,sha256=UGP3uWjF8pKQgfPifmdfvS5URVmzSg6m2NkS7LGzmio,9257
sympy/solvers/tests/test_polysys.py,sha256=P1Jk79CAYB85L-O3KRJKpsqvwVJgqqJ_u44NigGWsaA,6873
sympy/solvers/tests/test_recurr.py,sha256=-OeghSg16GFN70y_RUXC6CF6VU_b7NXaKDbejtRSocg,11418
sympy/solvers/tests/test_simplex.py,sha256=pG7j7KxXrlK8K_fF04XH_mvMzwEOopSBpJZiagKjyNs,9037
sympy/solvers/tests/test_solvers.py,sha256=wT6aQ25_31T7McT9FKY3LgnIxP8ab1uahjl2_xqld9o,106794
sympy/solvers/tests/test_solveset.py,sha256=lOQ4hG7Dg6yuDgFriKIlvWdzEumehgL08Srsh4JwDHY,148480
sympy/stats/__init__.py,sha256=9pY3OMdvAIeJ_Q1ZqynOZ8hHWFFrrxOx0qBHKkGfHCg,8487
sympy/stats/__pycache__/__init__.cpython-310.pyc,,
sympy/stats/__pycache__/compound_rv.cpython-310.pyc,,
sympy/stats/__pycache__/crv.cpython-310.pyc,,
sympy/stats/__pycache__/crv_types.cpython-310.pyc,,
sympy/stats/__pycache__/drv.cpython-310.pyc,,
sympy/stats/__pycache__/drv_types.cpython-310.pyc,,
sympy/stats/__pycache__/error_prop.cpython-310.pyc,,
sympy/stats/__pycache__/frv.cpython-310.pyc,,
sympy/stats/__pycache__/frv_types.cpython-310.pyc,,
sympy/stats/__pycache__/joint_rv.cpython-310.pyc,,
sympy/stats/__pycache__/joint_rv_types.cpython-310.pyc,,
sympy/stats/__pycache__/matrix_distributions.cpython-310.pyc,,
sympy/stats/__pycache__/random_matrix.cpython-310.pyc,,
sympy/stats/__pycache__/random_matrix_models.cpython-310.pyc,,
sympy/stats/__pycache__/rv.cpython-310.pyc,,
sympy/stats/__pycache__/rv_interface.cpython-310.pyc,,
sympy/stats/__pycache__/stochastic_process.cpython-310.pyc,,
sympy/stats/__pycache__/stochastic_process_types.cpython-310.pyc,,
sympy/stats/__pycache__/symbolic_multivariate_probability.cpython-310.pyc,,
sympy/stats/__pycache__/symbolic_probability.cpython-310.pyc,,
sympy/stats/compound_rv.py,sha256=SO1KXJ0aHGbD5y9QA8o6qOHbio3ua8wyO2Rsh0Hnw48,7965
sympy/stats/crv.py,sha256=jd8iemE41aW-byXgFDYKaMv2VOOUnyUtiMx_QAXI-n4,21028
sympy/stats/crv_types.py,sha256=qXYxtvJPg4ORJI7AxIkRhwnJ6lcz3sVBWf4toQy9vvA,122371
sympy/stats/drv.py,sha256=ewxYnUlCyvaF5ceMpziiz4e6FAgknzP5cC1ZVvQ_YLE,11995
sympy/stats/drv_types.py,sha256=q7MjAtpLjO2nFxnQOKfw_Ipf2-gYzlavbqrEcUjMQlw,19288
sympy/stats/error_prop.py,sha256=a-H6GZEidsiP_4-iNw7nSD99AMyN6DNHsSl0IUZGIAs,3315
sympy/stats/frv.py,sha256=vZROeD6DSVGX0kfOL_yOds3pZgjSiXFX-bMZtSUkVMA,16874
sympy/stats/frv_types.py,sha256=UXD-5fTjmoHQVO9ru32gVufxmKUR9lm_DmR-Qw-drtQ,23226
sympy/stats/joint_rv.py,sha256=DcixlO2Ml4gnwMmZk2VTegiHVq88DkLdQlOTQ57SQtc,15963
sympy/stats/joint_rv_types.py,sha256=PUatR4WcPHmAHadt8iRh5xYh5NJigzYh-EoAMR5blDw,30575
sympy/stats/matrix_distributions.py,sha256=3OricwEMM_NU8b2lJxoiSTml7kvqrNQ6IUIn9Xy_DsY,21953
sympy/stats/random_matrix.py,sha256=NmzLC5JMDWI2TvH8tY6go8lYyHmqcZ-B7sSIO7z7oAk,1028
sympy/stats/random_matrix_models.py,sha256=7i5XAUYxt-ekmP5KDMaytUlmCvxglEspoWbswSf82tE,15328
sympy/stats/rv.py,sha256=iXXytpYpwpg_FSZ6VqqNA_5Ik0pUJRd-Z_CUxeSAi0k,54479
sympy/stats/rv_interface.py,sha256=m4XsyoxtS7vUvj6P28Y4FvLAXrJLdoLasAp2L3dUTww,13937
sympy/stats/sampling/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/stats/sampling/__pycache__/__init__.cpython-310.pyc,,
sympy/stats/sampling/__pycache__/sample_numpy.cpython-310.pyc,,
sympy/stats/sampling/__pycache__/sample_pymc.cpython-310.pyc,,
sympy/stats/sampling/__pycache__/sample_scipy.cpython-310.pyc,,
sympy/stats/sampling/sample_numpy.py,sha256=B4ZC7ZBrSD6ICQT468rOy-xrOgQDuecsHa0zJesAeYE,4229
sympy/stats/sampling/sample_pymc.py,sha256=9g-n04aXSFc6F7FJ5zTYtHHL6W8-26g1nrgtamJc3Hw,2995
sympy/stats/sampling/sample_scipy.py,sha256=ysqpDy8bp1RMH0g5FFgMmp2SQuXGFkcSH7JDZEpiZ8w,6329
sympy/stats/sampling/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/stats/sampling/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/stats/sampling/tests/__pycache__/test_sample_continuous_rv.cpython-310.pyc,,
sympy/stats/sampling/tests/__pycache__/test_sample_discrete_rv.cpython-310.pyc,,
sympy/stats/sampling/tests/__pycache__/test_sample_finite_rv.cpython-310.pyc,,
sympy/stats/sampling/tests/test_sample_continuous_rv.py,sha256=Gh8hFN1hFFsthEv9wP2ZdgghQfaEnE8n7HlmyXXhN1E,5708
sympy/stats/sampling/tests/test_sample_discrete_rv.py,sha256=q8ZXAzJ0gHDAzIQFq6xsuGYmkpmWGlmX9adHRUqFTUo,3396
sympy/stats/sampling/tests/test_sample_finite_rv.py,sha256=dWwrFePw8eX2rBheAXi1AVxr_gqBD63VZKfW81hNoQc,3061
sympy/stats/stochastic_process.py,sha256=pDz0rbKXTiaNmMmmz70dP3F_KWL_XhoCKFHYBNt1QeU,2312
sympy/stats/stochastic_process_types.py,sha256=tEiQQPBxz1wfdRTf3HcTcA1bHl6eBT3gc4O-GcwB8VY,88632
sympy/stats/symbolic_multivariate_probability.py,sha256=R6Co7XCcxLoOtTqC6ZSnGuylZNUBrC5AD0DrJr2jE1A,10450
sympy/stats/symbolic_probability.py,sha256=HkWUiH9EM_DQ1aM1FxNmBm_8xtqeviIsyUpqFl2RYnI,23266
sympy/stats/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/stats/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/stats/tests/__pycache__/test_compound_rv.cpython-310.pyc,,
sympy/stats/tests/__pycache__/test_continuous_rv.cpython-310.pyc,,
sympy/stats/tests/__pycache__/test_discrete_rv.cpython-310.pyc,,
sympy/stats/tests/__pycache__/test_error_prop.cpython-310.pyc,,
sympy/stats/tests/__pycache__/test_finite_rv.cpython-310.pyc,,
sympy/stats/tests/__pycache__/test_joint_rv.cpython-310.pyc,,
sympy/stats/tests/__pycache__/test_matrix_distributions.cpython-310.pyc,,
sympy/stats/tests/__pycache__/test_mix.cpython-310.pyc,,
sympy/stats/tests/__pycache__/test_random_matrix.cpython-310.pyc,,
sympy/stats/tests/__pycache__/test_rv.cpython-310.pyc,,
sympy/stats/tests/__pycache__/test_stochastic_process.cpython-310.pyc,,
sympy/stats/tests/__pycache__/test_symbolic_multivariate.cpython-310.pyc,,
sympy/stats/tests/__pycache__/test_symbolic_probability.cpython-310.pyc,,
sympy/stats/tests/test_compound_rv.py,sha256=2927chbHTThA34Ki-ji319QT7ajQ1ueC640Mga-18ZA,6263
sympy/stats/tests/test_continuous_rv.py,sha256=BcBkiyX7e1QiwS6xnD-qLzXnijvqbsebTzXEn3IfGyE,56140
sympy/stats/tests/test_discrete_rv.py,sha256=kr3MjfI02cPvQrQISwmsIDEEh2gpMnzZsjMd5TOhAl0,10676
sympy/stats/tests/test_error_prop.py,sha256=xKAkw3F5XJ72xiDREI7PkyReWNVW_89CD_mjOY_diDY,1933
sympy/stats/tests/test_finite_rv.py,sha256=JHYgY4snFF5t9qcnQfKaN5zaGsO7_SuNR7Tq234W4No,20413
sympy/stats/tests/test_joint_rv.py,sha256=W28rCRYczv5Jax7k-bj7OveT-y-AP4q-kRR0-LNaWX0,18653
sympy/stats/tests/test_matrix_distributions.py,sha256=9daJUiSGaLq34TeZfB-xPqC8xz6vECGrm0DdBZaQPyY,8857
sympy/stats/tests/test_mix.py,sha256=Cplnw06Ki96Y_4fx6Bu7lUXjxoIfX7tNJasm9SOz5wQ,3991
sympy/stats/tests/test_random_matrix.py,sha256=CiD1hV25MGHwTfHGaoaehGD3iJ4lqNYi-ZiwReO6CVk,5842
sympy/stats/tests/test_rv.py,sha256=Bp7UwffIMO7oc8UnFV11yYGcXUjSa0NhsuOgQaNRMt8,12959
sympy/stats/tests/test_stochastic_process.py,sha256=i-VCOZrjpJtvyTBm9xgniTCkk_iUYIuFnkiyxzkn6Ig,39323
sympy/stats/tests/test_symbolic_multivariate.py,sha256=G3AgbRbt0DQ-p0DYXYDjbx4e4f5FIgd31F34e0NO2n8,5580
sympy/stats/tests/test_symbolic_probability.py,sha256=k5trScMiwSgl9dzJt30BV-t0KuYcyD-s9HtT2-hVhQ0,9398
sympy/strategies/__init__.py,sha256=XaTAPqDoi6527juvR8LLN1mv6ZcslDrGloTTBMjJzxA,1402
sympy/strategies/__pycache__/__init__.cpython-310.pyc,,
sympy/strategies/__pycache__/core.cpython-310.pyc,,
sympy/strategies/__pycache__/rl.cpython-310.pyc,,
sympy/strategies/__pycache__/tools.cpython-310.pyc,,
sympy/strategies/__pycache__/traverse.cpython-310.pyc,,
sympy/strategies/__pycache__/tree.cpython-310.pyc,,
sympy/strategies/__pycache__/util.cpython-310.pyc,,
sympy/strategies/branch/__init__.py,sha256=xxbMwR2LzLcQWsH9ss8ddE99VHFJTY-cYiR6xhO3tj0,356
sympy/strategies/branch/__pycache__/__init__.cpython-310.pyc,,
sympy/strategies/branch/__pycache__/core.cpython-310.pyc,,
sympy/strategies/branch/__pycache__/tools.cpython-310.pyc,,
sympy/strategies/branch/__pycache__/traverse.cpython-310.pyc,,
sympy/strategies/branch/core.py,sha256=QiXSa7uhvmUBTLyUwBQHrYkWlOceKh5p4kVD90VnCKM,2759
sympy/strategies/branch/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/strategies/branch/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/strategies/branch/tests/__pycache__/test_core.cpython-310.pyc,,
sympy/strategies/branch/tests/__pycache__/test_tools.cpython-310.pyc,,
sympy/strategies/branch/tests/__pycache__/test_traverse.cpython-310.pyc,,
sympy/strategies/branch/tests/test_core.py,sha256=23KQWJxC_2T1arwMAkt9pY1ZtG59avlxTZcVTn81UPI,2246
sympy/strategies/branch/tests/test_tools.py,sha256=4BDkqVqrTlsivQ0PldQr6PjVZsAikc39tSxGAQA3ir8,942
sympy/strategies/branch/tests/test_traverse.py,sha256=6rikMnZdamSzww1sSiM-aQwqa4lQrpM-DpOU9XCbiOQ,1322
sympy/strategies/branch/tools.py,sha256=tvv3IjmQGNYbo-slCbbDf_rylZd537wvLcpdBtT-bbY,357
sympy/strategies/branch/traverse.py,sha256=7iBViQdNpKu-AHoFED7_C9KBSyYcQBfLGopEJQbNtvk,799
sympy/strategies/core.py,sha256=nsH6LZgyc_aslv4Na5XvJMEizC6uSzscRlVW91k1pu4,3956
sympy/strategies/rl.py,sha256=I2puD2khbCmO3e9_ngUnclLgk1c-xBHeUf-bZu5haLM,4403
sympy/strategies/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/strategies/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/strategies/tests/__pycache__/test_core.cpython-310.pyc,,
sympy/strategies/tests/__pycache__/test_rl.cpython-310.pyc,,
sympy/strategies/tests/__pycache__/test_tools.cpython-310.pyc,,
sympy/strategies/tests/__pycache__/test_traverse.cpython-310.pyc,,
sympy/strategies/tests/__pycache__/test_tree.cpython-310.pyc,,
sympy/strategies/tests/test_core.py,sha256=42XHlv1hN1S1QPEf2r9pddZ2EQL6o4FEPQvfo-UmXcw,2152
sympy/strategies/tests/test_rl.py,sha256=wm0L6pdvddBgRcwhpiSk-nCgyzVGickfnOCkmHWS0j4,1949
sympy/strategies/tests/test_tools.py,sha256=UdMojFIn3f1b2x2iRGv1Wfnwdso-Kl57GTyjCU_DjzQ,875
sympy/strategies/tests/test_traverse.py,sha256=jWuZhYEt-F18_rxEMhn6OgGQ1GNs-dM_GFZ2F5nHs2I,2082
sympy/strategies/tests/test_tree.py,sha256=9NL948rt6i9tYU6CQz9VNxE6l1begQs-MxP2euzE3Sc,2400
sympy/strategies/tools.py,sha256=ERASzEP2SP-EcJ8p-4XyREYB15q3t81x1cyamJ-M880,1368
sympy/strategies/traverse.py,sha256=DhPnBJ5Rw_xzhGiBtSciTyV-H2zhlxgjYVjrNH-gLyk,1183
sympy/strategies/tree.py,sha256=ggnP9l3NIpJsssBMVKr4-yM_m8uCkrkm191ZC6MfZjc,3770
sympy/strategies/util.py,sha256=2fbR813IY4IYco5mBoGJLu5z88OhXmwuIxgOO9IvZO4,361
sympy/tensor/__init__.py,sha256=VMNXCRSayigQT6a3cvf5M_M-wdV-KSil_JbAmHcuUQc,870
sympy/tensor/__pycache__/__init__.cpython-310.pyc,,
sympy/tensor/__pycache__/functions.cpython-310.pyc,,
sympy/tensor/__pycache__/index_methods.cpython-310.pyc,,
sympy/tensor/__pycache__/indexed.cpython-310.pyc,,
sympy/tensor/__pycache__/tensor.cpython-310.pyc,,
sympy/tensor/__pycache__/toperators.cpython-310.pyc,,
sympy/tensor/array/__init__.py,sha256=lTT1EwV5tb3WAvmmS_mIjhCSWSLiB0NNPW4n9_3fu0k,8244
sympy/tensor/array/__pycache__/__init__.cpython-310.pyc,,
sympy/tensor/array/__pycache__/array_comprehension.cpython-310.pyc,,
sympy/tensor/array/__pycache__/array_derivatives.cpython-310.pyc,,
sympy/tensor/array/__pycache__/arrayop.cpython-310.pyc,,
sympy/tensor/array/__pycache__/dense_ndim_array.cpython-310.pyc,,
sympy/tensor/array/__pycache__/mutable_ndim_array.cpython-310.pyc,,
sympy/tensor/array/__pycache__/ndim_array.cpython-310.pyc,,
sympy/tensor/array/__pycache__/sparse_ndim_array.cpython-310.pyc,,
sympy/tensor/array/array_comprehension.py,sha256=01PTIbkAGaq0CDcaI_2KsaMnYm1nxQ8sFAiHHcc__gw,12262
sympy/tensor/array/array_derivatives.py,sha256=c-gYeA_qpXOY3aexyz7psSqmTVIGVBrcGDvSkW5dZV0,4796
sympy/tensor/array/arrayop.py,sha256=KvFGYWcYvChWkThtVAotlaSrcfjHogAxvpxWvp6dSgo,18397
sympy/tensor/array/dense_ndim_array.py,sha256=Ie8qVMJyp2Tsq7aVhmZpPX8X-KTlF9uaxkQfTzCZ9z8,6433
sympy/tensor/array/expressions/__init__.py,sha256=OUMJjZY7HtWJL0ygqkdWC8LdCqibJZhHCfYeXu-eB4E,7045
sympy/tensor/array/expressions/__pycache__/__init__.cpython-310.pyc,,
sympy/tensor/array/expressions/__pycache__/array_expressions.cpython-310.pyc,,
sympy/tensor/array/expressions/__pycache__/arrayexpr_derivatives.cpython-310.pyc,,
sympy/tensor/array/expressions/__pycache__/conv_array_to_indexed.cpython-310.pyc,,
sympy/tensor/array/expressions/__pycache__/conv_array_to_matrix.cpython-310.pyc,,
sympy/tensor/array/expressions/__pycache__/conv_indexed_to_array.cpython-310.pyc,,
sympy/tensor/array/expressions/__pycache__/conv_matrix_to_array.cpython-310.pyc,,
sympy/tensor/array/expressions/__pycache__/from_array_to_indexed.cpython-310.pyc,,
sympy/tensor/array/expressions/__pycache__/from_array_to_matrix.cpython-310.pyc,,
sympy/tensor/array/expressions/__pycache__/from_indexed_to_array.cpython-310.pyc,,
sympy/tensor/array/expressions/__pycache__/from_matrix_to_array.cpython-310.pyc,,
sympy/tensor/array/expressions/__pycache__/utils.cpython-310.pyc,,
sympy/tensor/array/expressions/array_expressions.py,sha256=FzaVng8PKZlydCAPCZwjOAnR7NevxzBiv7qQ_dRg2us,76990
sympy/tensor/array/expressions/arrayexpr_derivatives.py,sha256=W9-bY2LL83lLSNHXItzqjOgvf-HIDbUXPoVw8uOymcg,6249
sympy/tensor/array/expressions/conv_array_to_indexed.py,sha256=BIwlQr7RKC8bZN3mR8ICC5TYOC9uasYcV0Zc1VNKmiE,445
sympy/tensor/array/expressions/conv_array_to_matrix.py,sha256=85YZBTZI4o9dJtKDJXXug_lJVLG8dT_22AT7l7DKoyE,416
sympy/tensor/array/expressions/conv_indexed_to_array.py,sha256=EyW52TplBxIx25mUDvI_5Tzc8LD6Mnp6XNW9wIw9pH4,254
sympy/tensor/array/expressions/conv_matrix_to_array.py,sha256=XYyqt0NsQSrgNpEkr8xTGeUhR7ZYeNljVFfVEF1K7vA,250
sympy/tensor/array/expressions/from_array_to_indexed.py,sha256=3YIcsAzWVWQRJYQS90uPvSl2dM7ZqLV_qt7E9-uYU28,3936
sympy/tensor/array/expressions/from_array_to_matrix.py,sha256=2vvFAVWIgH_6ye0Fy8J1TnwGwZgXvtgX4g6iBtY_Ark,41304
sympy/tensor/array/expressions/from_indexed_to_array.py,sha256=RUcKemmrwuK5RFRr19YSPVMCOkZfLAWlbbB56u8Wi0g,11187
sympy/tensor/array/expressions/from_matrix_to_array.py,sha256=yIY1RupF9-FVV3jZLsqWxZ1ckoE1-HkQyM8cQIm4_Gs,3929
sympy/tensor/array/expressions/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/tensor/array/expressions/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/tensor/array/expressions/tests/__pycache__/test_array_expressions.cpython-310.pyc,,
sympy/tensor/array/expressions/tests/__pycache__/test_arrayexpr_derivatives.cpython-310.pyc,,
sympy/tensor/array/expressions/tests/__pycache__/test_as_explicit.cpython-310.pyc,,
sympy/tensor/array/expressions/tests/__pycache__/test_convert_array_to_indexed.cpython-310.pyc,,
sympy/tensor/array/expressions/tests/__pycache__/test_convert_array_to_matrix.cpython-310.pyc,,
sympy/tensor/array/expressions/tests/__pycache__/test_convert_indexed_to_array.cpython-310.pyc,,
sympy/tensor/array/expressions/tests/__pycache__/test_convert_matrix_to_array.cpython-310.pyc,,
sympy/tensor/array/expressions/tests/__pycache__/test_deprecated_conv_modules.cpython-310.pyc,,
sympy/tensor/array/expressions/tests/test_array_expressions.py,sha256=QUAdxQ9TvBpDEAZoJpLSWwbqjmuflPe3xBRP30lFZr0,31262
sympy/tensor/array/expressions/tests/test_arrayexpr_derivatives.py,sha256=lpC4ly6MJLDRBcVt3GcP3H6ke9bI-o3VULw0xyF5QbY,2470
sympy/tensor/array/expressions/tests/test_as_explicit.py,sha256=nOjFKXCqYNu2O7Szc1TD1x1bsUchPRAG3nGlNGEd1Yg,2568
sympy/tensor/array/expressions/tests/test_convert_array_to_indexed.py,sha256=6yNxGXH6BX5607FTjMkwR2t9wNVlEhV8JMSh4UIWux8,2500
sympy/tensor/array/expressions/tests/test_convert_array_to_matrix.py,sha256=2vkSep9CPKYrQQS0u8Ayn_sc7yek1zwzjjCWK5cfYe8,29311
sympy/tensor/array/expressions/tests/test_convert_indexed_to_array.py,sha256=RVEG_qUsXiBH9gHtWp2-9pMC4J2aLc4iUdzBFM0QyTw,8615
sympy/tensor/array/expressions/tests/test_convert_matrix_to_array.py,sha256=G2g5E0l-FABwYyQowbKKvLcEI8NViJXaYLW3eUEcvjw,4595
sympy/tensor/array/expressions/tests/test_deprecated_conv_modules.py,sha256=DG8IoUtxCy2acWjUHUUKu4bRsTxXbeFLFjKMLA2GdLY,1216
sympy/tensor/array/expressions/utils.py,sha256=Rn58boHHUEoBZFtinDpruLWFBkNBwgkVQ4c9m7Nym1o,3939
sympy/tensor/array/mutable_ndim_array.py,sha256=M0PTt8IOIcVXqQPWe2N50sm4Eq2bodRXV4Vkd08crXk,277
sympy/tensor/array/ndim_array.py,sha256=wWbDnFdZQ8ZCRtNGAwNcF3Vo_skzulZRjKrMFGATIqs,19045
sympy/tensor/array/sparse_ndim_array.py,sha256=4nD_Hg-JdC_1mYQTohmKFfL5M1Ugdq0fpnDUILkTtq8,6387
sympy/tensor/array/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/tensor/array/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/tensor/array/tests/__pycache__/test_array_comprehension.cpython-310.pyc,,
sympy/tensor/array/tests/__pycache__/test_array_derivatives.cpython-310.pyc,,
sympy/tensor/array/tests/__pycache__/test_arrayop.cpython-310.pyc,,
sympy/tensor/array/tests/__pycache__/test_immutable_ndim_array.cpython-310.pyc,,
sympy/tensor/array/tests/__pycache__/test_mutable_ndim_array.cpython-310.pyc,,
sympy/tensor/array/tests/__pycache__/test_ndim_array.cpython-310.pyc,,
sympy/tensor/array/tests/__pycache__/test_ndim_array_conversions.cpython-310.pyc,,
sympy/tensor/array/tests/test_array_comprehension.py,sha256=e8MsWbvwmr-HxTfaM7i8HoIVofa8InLzF9PTCIVvzjU,4529
sympy/tensor/array/tests/test_array_derivatives.py,sha256=hS10Bkb3F2kWoFoxk2ucr21p0r5DfwHDYPI8HcmAl0o,1601
sympy/tensor/array/tests/test_arrayop.py,sha256=WahGcUnArsAo9eaMqGT7_AjKons0WgFzLOWTtNvnSEI,25844
sympy/tensor/array/tests/test_immutable_ndim_array.py,sha256=9ji_14szn-qoL6DQ5muzIFNaXefT7n55PFigXoFwk50,15823
sympy/tensor/array/tests/test_mutable_ndim_array.py,sha256=rFFa0o0AJYgPNnpqijl91Vb9EW2kgHGQc6cu9f1fIvY,13070
sympy/tensor/array/tests/test_ndim_array.py,sha256=KH-9LAME3ldVIu5n7Vd_Xr36dN4frCdiF9qZdBWETu0,2232
sympy/tensor/array/tests/test_ndim_array_conversions.py,sha256=CUGDCbCcslACy3Ngq-zoig9JnO4yHTw3IPcKy0FnRpw,648
sympy/tensor/functions.py,sha256=FNZ2M0HGVaASJ_1AkPu1vF5NHNLmBXKLsj0Q5wKMX90,4168
sympy/tensor/index_methods.py,sha256=dcX9kNKLHi_XXkFHBPS-fcM-PaeYKkX80jmzxC0siiQ,15434
sympy/tensor/indexed.py,sha256=3qiQehMvXSDbr_RDXg6l_aEu6dHZXEfXrMoXCJj7Cqo,24515
sympy/tensor/tensor.py,sha256=M2E29xnOv_IeE8taTatAHwTuNfNANOpDj4MxtbsrkYA,168384
sympy/tensor/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/tensor/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/tensor/tests/__pycache__/test_functions.cpython-310.pyc,,
sympy/tensor/tests/__pycache__/test_index_methods.cpython-310.pyc,,
sympy/tensor/tests/__pycache__/test_indexed.cpython-310.pyc,,
sympy/tensor/tests/__pycache__/test_printing.cpython-310.pyc,,
sympy/tensor/tests/__pycache__/test_tensor.cpython-310.pyc,,
sympy/tensor/tests/__pycache__/test_tensor_element.cpython-310.pyc,,
sympy/tensor/tests/__pycache__/test_tensor_operators.cpython-310.pyc,,
sympy/tensor/tests/test_functions.py,sha256=rBBHjJIUA2oR83UgEJ_GIASDWfTZXDzOllmcO90XYDU,1552
sympy/tensor/tests/test_index_methods.py,sha256=Pu951z4yYYMOXBKcNteH63hTAxmNX8702nSQH_pciFE,7112
sympy/tensor/tests/test_indexed.py,sha256=EB2-t0gYkaTOvrCkhyW2cEL_MVi4xBUZxbf35NA-puI,16376
sympy/tensor/tests/test_printing.py,sha256=sUx_rChNTWFKPNwVl296QXO-d4-yemDJnkEHFislsmc,424
sympy/tensor/tests/test_tensor.py,sha256=YKO8G8OIlpXKJ9A1RwvNxstAeAsFUGPGAKVdJwh-ThM,75921
sympy/tensor/tests/test_tensor_element.py,sha256=1dF96FtqUGaJzethw23vJIj3H5KdxsU1Xyd4DU54EB4,908
sympy/tensor/tests/test_tensor_operators.py,sha256=sOwu-U28098Lg0iV_9RfYxvJ8wAd5Rk6_vAivWdkc9Q,17945
sympy/tensor/toperators.py,sha256=fniTUpdYz0OvtNnFgrHINedX86FxVcxfKj9l_l1p9Rw,8840
sympy/testing/__init__.py,sha256=IN9aHvoksdZywonAYE0cExFnuPQs9z1E4P742aYZNnE,169
sympy/testing/__pycache__/__init__.cpython-310.pyc,,
sympy/testing/__pycache__/matrices.cpython-310.pyc,,
sympy/testing/__pycache__/pytest.cpython-310.pyc,,
sympy/testing/__pycache__/quality_unicode.cpython-310.pyc,,
sympy/testing/__pycache__/randtest.cpython-310.pyc,,
sympy/testing/__pycache__/runtests.cpython-310.pyc,,
sympy/testing/__pycache__/runtests_pytest.cpython-310.pyc,,
sympy/testing/__pycache__/tmpfiles.cpython-310.pyc,,
sympy/testing/matrices.py,sha256=VWBPdjIUYNHE7fdbYcmQwQTYcIWpOP9tFn9A0rGCBmE,216
sympy/testing/pytest.py,sha256=LCU0pOT-Oyx4Gmbw5h1jKJE10abWPSykGYaM7ZwY5aI,13480
sympy/testing/quality_unicode.py,sha256=quKFUpEUPftVGNpsj71WpRoeKZUzc8sgLjODRmHPf-w,3482
sympy/testing/randtest.py,sha256=IKDFAm8b72Z1OkT7vpgnZjaW5LsSU_wf6g35sCkq9I0,562
sympy/testing/runtests.py,sha256=zHOsW3xcp5YsaBhhEPXOALnWSpiyPBa2PZhrhg3VN4Q,89945
sympy/testing/runtests_pytest.py,sha256=OqMuoPKVOsIMiiMljANwB8Qupmkjg_SustWxe5W2Rho,18327
sympy/testing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/testing/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/testing/tests/__pycache__/diagnose_imports.cpython-310.pyc,,
sympy/testing/tests/__pycache__/test_code_quality.cpython-310.pyc,,
sympy/testing/tests/__pycache__/test_deprecated.cpython-310.pyc,,
sympy/testing/tests/__pycache__/test_module_imports.cpython-310.pyc,,
sympy/testing/tests/__pycache__/test_pytest.cpython-310.pyc,,
sympy/testing/tests/__pycache__/test_runtests_pytest.cpython-310.pyc,,
sympy/testing/tests/diagnose_imports.py,sha256=i6R3_GpTGEVqpnqFXCYF89Q-Yxp_SAV0wOKnkdP-ExI,9588
sympy/testing/tests/test_code_quality.py,sha256=3BhN-Pt1fuwPpUvviC5xXxgxQ8LTTDX0XhFfvT96HAw,19222
sympy/testing/tests/test_deprecated.py,sha256=wQZHs4wDNuK4flaKKLsJW6XRMtrVjMv_5rUP3WspgPA,183
sympy/testing/tests/test_module_imports.py,sha256=5w6F6JW6K7lgpbB4X9Tj0Vw8AcNVlfaSuvbwKXJKD6c,1459
sympy/testing/tests/test_pytest.py,sha256=iKO10Tvua1Xem6a22IWH4SDrpFfr-bM-rXx039Ua7YA,6778
sympy/testing/tests/test_runtests_pytest.py,sha256=oapv5Sf_taDyZouYSYcUxIC8AOSJKeVydKDBtgHJhkw,6338
sympy/testing/tmpfiles.py,sha256=bF8ktKC9lDhS65gahB9hOewsZ378UkhLgq3QHiqWYXU,1042
sympy/this.py,sha256=XfOkN5EIM2RuDxSm_q6k_R_WtkIoSy6PXWKp3aAXvoc,550
sympy/unify/__init__.py,sha256=Upa9h7SSr9W1PXo0WkNESsGsMZ85rcWkeruBtkAi3Fg,293
sympy/unify/__pycache__/__init__.cpython-310.pyc,,
sympy/unify/__pycache__/core.cpython-310.pyc,,
sympy/unify/__pycache__/rewrite.cpython-310.pyc,,
sympy/unify/__pycache__/usympy.cpython-310.pyc,,
sympy/unify/core.py,sha256=-BCNPPMdfZuhhIWqyn9pYJoO8yFPGDX78Hn2551ABuE,7037
sympy/unify/rewrite.py,sha256=Emr8Uoum3gxKpMDqFHJIjx3xChArUIN6XIy6NPfCS8I,1798
sympy/unify/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/unify/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/unify/tests/__pycache__/test_rewrite.cpython-310.pyc,,
sympy/unify/tests/__pycache__/test_sympy.cpython-310.pyc,,
sympy/unify/tests/__pycache__/test_unify.cpython-310.pyc,,
sympy/unify/tests/test_rewrite.py,sha256=BgA8zmdz9Nw-Xbu4-w3UABeWypqLvmy9VzL744EmYtE,2002
sympy/unify/tests/test_sympy.py,sha256=UCItZJNAx9dG5F7O27pyXUF1-e6aOwkZ-cVdB6SZFZc,5922
sympy/unify/tests/test_unify.py,sha256=4TlgchV6NWuBekJx9RGlMjx3-UwonzgIYXDytb7sBRU,3029
sympy/unify/usympy.py,sha256=6Kxx96FXSdqXimLseVK_FkYwy2vqWhNnxMVPMRShvy4,3964
sympy/utilities/__init__.py,sha256=nbQhzII8dw5zd4hQJ2SUyriK5dOrqf-bbjy10XKQXPw,840
sympy/utilities/__pycache__/__init__.cpython-310.pyc,,
sympy/utilities/__pycache__/autowrap.cpython-310.pyc,,
sympy/utilities/__pycache__/codegen.cpython-310.pyc,,
sympy/utilities/__pycache__/decorator.cpython-310.pyc,,
sympy/utilities/__pycache__/enumerative.cpython-310.pyc,,
sympy/utilities/__pycache__/exceptions.cpython-310.pyc,,
sympy/utilities/__pycache__/iterables.cpython-310.pyc,,
sympy/utilities/__pycache__/lambdify.cpython-310.pyc,,
sympy/utilities/__pycache__/magic.cpython-310.pyc,,
sympy/utilities/__pycache__/matchpy_connector.cpython-310.pyc,,
sympy/utilities/__pycache__/memoization.cpython-310.pyc,,
sympy/utilities/__pycache__/misc.cpython-310.pyc,,
sympy/utilities/__pycache__/pkgdata.cpython-310.pyc,,
sympy/utilities/__pycache__/pytest.cpython-310.pyc,,
sympy/utilities/__pycache__/randtest.cpython-310.pyc,,
sympy/utilities/__pycache__/runtests.cpython-310.pyc,,
sympy/utilities/__pycache__/source.cpython-310.pyc,,
sympy/utilities/__pycache__/timeutils.cpython-310.pyc,,
sympy/utilities/__pycache__/tmpfiles.cpython-310.pyc,,
sympy/utilities/_compilation/__init__.py,sha256=uYUDPbwrMTbGEMVuago32EN_ix8fsi5M0SvcLOtwMOk,751
sympy/utilities/_compilation/__pycache__/__init__.cpython-310.pyc,,
sympy/utilities/_compilation/__pycache__/availability.cpython-310.pyc,,
sympy/utilities/_compilation/__pycache__/compilation.cpython-310.pyc,,
sympy/utilities/_compilation/__pycache__/runners.cpython-310.pyc,,
sympy/utilities/_compilation/__pycache__/util.cpython-310.pyc,,
sympy/utilities/_compilation/availability.py,sha256=ybxp3mboH5772JHTWKBN1D-cs6QxATQiaL4zJVV4RE0,2884
sympy/utilities/_compilation/compilation.py,sha256=vt4TPWSElcq38DmEVAqC6l1AtE_wShfI7J-OuFcpTe8,22161
sympy/utilities/_compilation/runners.py,sha256=KAinYF0m55dRdDFO5pvHBlvTxDY0ldoImqTOwX7VmL4,10237
sympy/utilities/_compilation/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/utilities/_compilation/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/utilities/_compilation/tests/__pycache__/test_compilation.cpython-310.pyc,,
sympy/utilities/_compilation/tests/test_compilation.py,sha256=pjNAiHfRs2itdR-5HfpnDpDbrnoZSnSFG6Pe3sn5fms,3078
sympy/utilities/_compilation/util.py,sha256=58YxRZMIirYQM26PKl6qNosJwGedK4_snT91FHT921Y,8612
sympy/utilities/autowrap.py,sha256=QOz55RICdNFq81MnmEIW0xEcy58SilJn1WSXXqDXeD8,41270
sympy/utilities/codegen.py,sha256=45U-8Z5pdF8fkwTj71l8wftki23KSiqTV3_qqggJ5tk,81671
sympy/utilities/decorator.py,sha256=XG4GpcLnF5nKX0uqgCOarrnoUzPlrEelxhldpo5BD2w,11108
sympy/utilities/enumerative.py,sha256=Vzts0A1xXO3vBFa5icdVIsb1T81qT6UvpUTGsbl-ldE,43620
sympy/utilities/exceptions.py,sha256=RoKY7jDIq6OsZbNSCyneWbVQb1Cw2MtOuioJlCKmBec,10570
sympy/utilities/iterables.py,sha256=noIMRQmdSPfbg1LorRPHreozS265u4IHUmDkOvMB2po,91103
sympy/utilities/lambdify.py,sha256=-9QLkNjkvTGP5J946an3SBb3WJMmEYxjCaD67uYjtl4,55234
sympy/utilities/magic.py,sha256=ofrwi1-xwMWb4VCQOEIwe4J1QAwxOscigDq26uSn3iY,400
sympy/utilities/matchpy_connector.py,sha256=7dSDOPbN5Y_XW6bIGVNK3dJ-gVdTB_liJ8O5rIqd28c,11948
sympy/utilities/mathml/__init__.py,sha256=74VhxNJlvCZTm2Jh3t69N8QZRklTQjqMvqFMLVwCKQk,3388
sympy/utilities/mathml/__pycache__/__init__.cpython-310.pyc,,
sympy/utilities/mathml/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/utilities/mathml/data/__pycache__/__init__.cpython-310.pyc,,
sympy/utilities/mathml/data/mmlctop.xsl,sha256=fi3CTNyg-mSscOGYBXLJv8veE_ItR_YTFMJ4jmjp6aE,114444
sympy/utilities/mathml/data/mmltex.xsl,sha256=haX7emZOfD6_nbn5BjK93F-C85mSS8KogAbIBsW1aBA,137304
sympy/utilities/mathml/data/simple_mmlctop.xsl,sha256=OM-Vge1satH-MAYwWhraeXcorn1KGtuqBK-PDddaOrk,114433
sympy/utilities/memoization.py,sha256=jD6RjVMZkGpNZYaJ9481vTiqvmwyu1IKDpsF5PYIvf4,1838
sympy/utilities/misc.py,sha256=nLpQIo2BipOBXfv5T001UIpLQoN_1_1hmrzxHIFyWGk,16006
sympy/utilities/pkgdata.py,sha256=BiBonyObCsS6EnLUII_1kDtchDN7Tur0T4GMzoUo06M,935
sympy/utilities/pytest.py,sha256=F9TGNtoNvQUdlt5HYU084ITNmc7__7MBCSLLulBlM_Y,435
sympy/utilities/randtest.py,sha256=aYUX_mgmQyfRdMjEOWaHM506CZ6WUK0eFuew0vFTwRs,430
sympy/utilities/runtests.py,sha256=hYnDNiFNnDjQcXG04_3lzPFbUz6i0AUZ2rZ_RECVoDo,446
sympy/utilities/source.py,sha256=ShIXRNtplSEfZNi5VDYD3yi6305eRz4TmchEOEvcicw,1127
sympy/utilities/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/utilities/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/utilities/tests/__pycache__/test_autowrap.cpython-310.pyc,,
sympy/utilities/tests/__pycache__/test_codegen.cpython-310.pyc,,
sympy/utilities/tests/__pycache__/test_codegen_julia.cpython-310.pyc,,
sympy/utilities/tests/__pycache__/test_codegen_octave.cpython-310.pyc,,
sympy/utilities/tests/__pycache__/test_codegen_rust.cpython-310.pyc,,
sympy/utilities/tests/__pycache__/test_decorator.cpython-310.pyc,,
sympy/utilities/tests/__pycache__/test_deprecated.cpython-310.pyc,,
sympy/utilities/tests/__pycache__/test_enumerative.cpython-310.pyc,,
sympy/utilities/tests/__pycache__/test_exceptions.cpython-310.pyc,,
sympy/utilities/tests/__pycache__/test_iterables.cpython-310.pyc,,
sympy/utilities/tests/__pycache__/test_lambdify.cpython-310.pyc,,
sympy/utilities/tests/__pycache__/test_matchpy_connector.cpython-310.pyc,,
sympy/utilities/tests/__pycache__/test_mathml.cpython-310.pyc,,
sympy/utilities/tests/__pycache__/test_misc.cpython-310.pyc,,
sympy/utilities/tests/__pycache__/test_pickling.cpython-310.pyc,,
sympy/utilities/tests/__pycache__/test_source.cpython-310.pyc,,
sympy/utilities/tests/__pycache__/test_timeutils.cpython-310.pyc,,
sympy/utilities/tests/__pycache__/test_wester.cpython-310.pyc,,
sympy/utilities/tests/__pycache__/test_xxe.cpython-310.pyc,,
sympy/utilities/tests/test_autowrap.py,sha256=dZKYsQhl1NV6VcWo6pmi5R8Wc8cep0pQ335U1mygAOs,14888
sympy/utilities/tests/test_codegen.py,sha256=pjfhZTa5LI9gdrakDfLtBBrIxgWjWORR_NBDW9J8Cq8,56130
sympy/utilities/tests/test_codegen_julia.py,sha256=kb3soJ1L7lTfZkYJKytfY_aKoHt6fkNjWhYblebzThw,18543
sympy/utilities/tests/test_codegen_octave.py,sha256=_yd9uGKHZzwUFpderSa9E2cYqt8JMcEtBuN6U7_7bJ0,17833
sympy/utilities/tests/test_codegen_rust.py,sha256=wJh6YmDfq8haGjJDniDaVUsDIKEj3rT_OB4r6uLI77Y,12323
sympy/utilities/tests/test_decorator.py,sha256=VYUvzUrVI7I7MK0YZxLLEmEu4pV5dqaB1CLEJ8Ocav4,3705
sympy/utilities/tests/test_deprecated.py,sha256=LRrZ2UxuXnK6Jwxl8vT0EdLT-q-7jLkTC69U9JjuYYU,489
sympy/utilities/tests/test_enumerative.py,sha256=aUw6nbSzBp8h_pk35YZ_uzRncRoLYStblodeiDRFk6I,6089
sympy/utilities/tests/test_exceptions.py,sha256=OKRa2yuHMtnVcnisu-xcaedi2RKsH9QrgU9exgoOK30,716
sympy/utilities/tests/test_iterables.py,sha256=xYWyoDtVkYfnIOdA-5yUYZsxo4iTsExaNpVmjMuwpTc,35288
sympy/utilities/tests/test_lambdify.py,sha256=qTV1o-YX5tY5ZOKuVnxBSmyxSs_s6YGMG08LqORIuuA,61429
sympy/utilities/tests/test_matchpy_connector.py,sha256=mBrAev2Hxe4jg_1ny3ZaGIfh2xvWbr6BDRVjB9owJFM,4850
sympy/utilities/tests/test_mathml.py,sha256=-6z1MRYEH4eYQi2_wt8zmdjwtt5Cn483zqsvD-o_r70,836
sympy/utilities/tests/test_misc.py,sha256=TxjUNCosyCR5w1iJ6o77yKB4WBLyirVhOaALGYdkN9k,4726
sympy/utilities/tests/test_pickling.py,sha256=CnYBnPKMS5iaenbRjUPNP5DaR34gmTtpz4jONFERoxk,23527
sympy/utilities/tests/test_source.py,sha256=ObjrJxZFVhLgXjVmFHUy7bti9UPPgOh5Cptw8lHW9mM,289
sympy/utilities/tests/test_timeutils.py,sha256=sCRC6BCSho1e9n4clke3QXHx4a3qYLru-bddS_sEmFA,337
sympy/utilities/tests/test_wester.py,sha256=S79UNsGhEW5z6hDriQB8OY_Vhaz1j0Q7yv31HynyDfk,94866
sympy/utilities/tests/test_xxe.py,sha256=xk1j0Dd96wsGYKRNDzXTW0hTQejGCfiZcEhYcYiqojg,66
sympy/utilities/timeutils.py,sha256=DUtQYONkJnWjU2FvAbvxuRMkGmXpLMeaiOcH7R9Os9o,1968
sympy/utilities/tmpfiles.py,sha256=yOjbs90sEtVc00YZyveyblT8zkwj4o70_RmuEKdKq_s,445
sympy/vector/__init__.py,sha256=8a4cSQ1sJ5uirdMoHnV7SWXU3zJPKt_0ojona8C-p1Y,1909
sympy/vector/__pycache__/__init__.cpython-310.pyc,,
sympy/vector/__pycache__/basisdependent.cpython-310.pyc,,
sympy/vector/__pycache__/coordsysrect.cpython-310.pyc,,
sympy/vector/__pycache__/deloperator.cpython-310.pyc,,
sympy/vector/__pycache__/dyadic.cpython-310.pyc,,
sympy/vector/__pycache__/functions.cpython-310.pyc,,
sympy/vector/__pycache__/implicitregion.cpython-310.pyc,,
sympy/vector/__pycache__/integrals.cpython-310.pyc,,
sympy/vector/__pycache__/operators.cpython-310.pyc,,
sympy/vector/__pycache__/orienters.cpython-310.pyc,,
sympy/vector/__pycache__/parametricregion.cpython-310.pyc,,
sympy/vector/__pycache__/point.cpython-310.pyc,,
sympy/vector/__pycache__/scalar.cpython-310.pyc,,
sympy/vector/__pycache__/vector.cpython-310.pyc,,
sympy/vector/basisdependent.py,sha256=2i24eFelUttZsg_bJZVTVJjWJCmJ7T4bNtZcsCN0nC8,11539
sympy/vector/coordsysrect.py,sha256=1C2JX5N4d3_U7LUEPRg18OjpMoiPRmcFKQEZT8KGCDc,36861
sympy/vector/deloperator.py,sha256=4BJNjmI342HkVRmeQkqauqvibKsf2HOuzknQTfQMkpg,3191
sympy/vector/dyadic.py,sha256=IOyrgONyGDHPtG0RINcMgetAVMSOmYI5a99s-OwXBTA,8571
sympy/vector/functions.py,sha256=auLfE1Su2kLtkRvlB_7Wol8O0_sqei1hojun3pkDRYI,15552
sympy/vector/implicitregion.py,sha256=FXAwrZRr5inyZyv_Mvtw6ClVhSI8LsFnMFCxfVYFXAg,16157
sympy/vector/integrals.py,sha256=x8DrvKXPznE05JgnZ7I3IWLWrvFl9SEghGaFmHrBaE4,6837
sympy/vector/operators.py,sha256=mI6d0eIxVcoDeH5PrhtPTzhxX_RXByX_4hjXeBTeq88,9521
sympy/vector/orienters.py,sha256=EtWNWfOvAuy_wipam9SA7_muKSrsP-43UPRCCz56sb0,11798
sympy/vector/parametricregion.py,sha256=3YyY0fkFNelR6ldi8XYRWpkFEvqY5-rFg_vT3NFute0,5932
sympy/vector/point.py,sha256=ozYlInnlsmIpKBEr5Ui331T1lnAB5zS2_pHYh9k_eMs,4516
sympy/vector/scalar.py,sha256=Z2f2wiK7BS73ctYTyNvn3gB74mXZuENpScLi_M1SpYg,1962
sympy/vector/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
sympy/vector/tests/__pycache__/__init__.cpython-310.pyc,,
sympy/vector/tests/__pycache__/test_coordsysrect.cpython-310.pyc,,
sympy/vector/tests/__pycache__/test_dyadic.cpython-310.pyc,,
sympy/vector/tests/__pycache__/test_field_functions.cpython-310.pyc,,
sympy/vector/tests/__pycache__/test_functions.cpython-310.pyc,,
sympy/vector/tests/__pycache__/test_implicitregion.cpython-310.pyc,,
sympy/vector/tests/__pycache__/test_integrals.cpython-310.pyc,,
sympy/vector/tests/__pycache__/test_operators.cpython-310.pyc,,
sympy/vector/tests/__pycache__/test_parametricregion.cpython-310.pyc,,
sympy/vector/tests/__pycache__/test_printing.cpython-310.pyc,,
sympy/vector/tests/__pycache__/test_vector.cpython-310.pyc,,
sympy/vector/tests/test_coordsysrect.py,sha256=q9n9OIG_CpD4KQN20dzwRZIXoMv7VSgp8fHmVnkZfr0,19595
sympy/vector/tests/test_dyadic.py,sha256=f1R-BL_63VBbc0XgEX_LYzV_3OupYd4hp5RzRk6dAbI,4949
sympy/vector/tests/test_field_functions.py,sha256=v9l8Ex8K2MsPGxqAPhpEgu6WAo6wS6qvdWLKQMxgE4A,14094
sympy/vector/tests/test_functions.py,sha256=Bs2sekdDJyw_wrUpG7vZQGH0y0S4C4AbxGSpeU_8C2s,8050
sympy/vector/tests/test_implicitregion.py,sha256=wVilD5H-MhHiW58QT6P5U7uT79JdKHm9D7JgZoi6BE4,4028
sympy/vector/tests/test_integrals.py,sha256=BVRhrr_JeAsCKv_E-kA2jaXB8ZXTfj7nkNgT5o-XOJc,5093
sympy/vector/tests/test_operators.py,sha256=KexUWvc_Nwp2HWrEbhxiO7MeaFxYlckrp__Tkwg-wmU,1613
sympy/vector/tests/test_parametricregion.py,sha256=OfKapF9A_g9X6JxgYc0UfxIhwXzRERzaj-EijQCJONw,4009
sympy/vector/tests/test_printing.py,sha256=3BeW55iQ4qXdfDTFqptE2ufJPJIBOzdfIYVx84n_EwA,7708
sympy/vector/tests/test_vector.py,sha256=Mo88Jgmy3CuSQz25WSH34EnZSs_JBY7E-OKPO2SjhPc,7861
sympy/vector/vector.py,sha256=pikmeLwkdW_6ed-Xo_U0_a2Om5TGSlfE4PijkRsJllc,17911
