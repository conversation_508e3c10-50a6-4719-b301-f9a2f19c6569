# 公司制度查询平台 - 零配置打包方案

## 方案概述

本方案实现了公司制度查询平台的零配置打包，确保在用户环境中：
- ✅ AI问答功能正常运行
- ✅ 制度检索功能完整可用  
- ✅ 向量数据库开箱即用
- ✅ 文档预览精准定位

## 技术架构

### 核心组件
1. **嵌入式Python 3.10环境** - 完整的Python运行时
2. **预编译AI依赖** - PyTorch、Transformers、Sentence-Transformers
3. **本地AI模型** - Qwen-1.5-1.8B + Text2Vec中文嵌入模型
4. **向量数据库** - ChromaDB + Whoosh全文索引
5. **GUI界面** - PyQt6 + WebEngine文档预览
6. **智能启动器** - 自适应路径和错误恢复

### 打包特点
- **零依赖安装** - 无需用户安装Python或任何依赖
- **绝对路径配置** - 避免相对路径问题
- **智能设备检测** - 自动选择CPU/GPU运行模式
- **增量更新支持** - 支持模型和数据更新
- **专业安装体验** - NSIS安装器 + 便携版

## 使用说明

### 1. 环境准备

确保开发环境具备：
```bash
# Python 3.8+
python --version

# 必要工具
pip install -r requirements.txt

# NSIS安装器（Windows）
# 下载并安装到 D:\software\NSIS\
```

### 2. 一键构建

运行完整构建脚本：
```bash
python build_complete.py
```

构建过程包括：
1. 🔍 检查构建前提条件
2. 📥 下载嵌入式Python环境
3. 📦 安装所有依赖包
4. 📁 复制应用程序和数据
5. ⚙️ 优化配置和路径
6. 🚀 创建智能启动器
7. 💿 生成NSIS安装器
8. 📁 创建便携版压缩包

### 3. 分步构建（可选）

如需分步执行：

```bash
# 1. 基础打包
python build_package.py

# 2. 配置优化
python optimize_config.py build_package

# 3. 创建启动器
python create_launcher.py

# 4. 测试验证
python test_package.py build_package
```

### 4. 输出结果

构建完成后，在 `dist/` 目录下生成：

```
dist/
├── CompanyPolicyQA_Setup_v1.0.0.exe     # NSIS安装器
├── CompanyPolicyQA_Portable_v1.0.0.zip  # 便携版压缩包
├── BUILD_REPORT.md                       # 构建报告
└── TEST_REPORT.md                        # 测试报告
```

## 部署方式

### 方式一：安装器部署（推荐）

1. **分发安装器**
   ```
   CompanyPolicyQA_Setup_v1.0.0.exe
   ```

2. **用户安装**
   - 双击运行安装器
   - 选择安装目录（默认：C:\Program Files\CompanyPolicyQA）
   - 选择组件（桌面快捷方式、开始菜单等）
   - 完成安装

3. **启动使用**
   - 桌面快捷方式启动
   - 或开始菜单启动
   - 首次运行自动初始化

### 方式二：便携版部署

1. **解压部署**
   ```bash
   # 解压到目标目录
   unzip CompanyPolicyQA_Portable_v1.0.0.zip
   ```

2. **启动使用**
   ```bash
   # 双击启动
   start_app.bat
   
   # 或命令行启动
   python smart_launcher.py
   ```

## 目录结构

```
CompanyPolicyQA/
├── python/                    # 嵌入式Python环境
│   ├── python.exe            # Python解释器
│   ├── Lib/                  # 标准库
│   └── site-packages/        # 第三方包
├── app/                      # 应用程序
│   ├── main.py              # 主程序入口
│   ├── config.yaml          # 配置文件
│   ├── src/                 # 源代码
│   ├── static/              # 静态资源
│   ├── models/              # AI模型文件
│   │   ├── qwen-1.5-1.8b-chat/
│   │   └── shibing624/
│   ├── data/                # 数据目录
│   │   ├── chroma_db/       # 向量数据库
│   │   ├── whoosh_index/    # 全文索引
│   │   └── preview_pdfs/    # PDF预览
│   ├── docs/                # 制度文档
│   └── logs/                # 日志文件
├── smart_launcher.py         # 智能启动器
├── start_app.bat            # 批处理启动器
└── README.md                # 使用说明
```

## 系统要求

### 最低要求
- **操作系统**: Windows 10/11 (64位)
- **内存**: 8GB RAM
- **存储**: 5GB 可用空间
- **处理器**: Intel i5 或 AMD 同等级别

### 推荐配置
- **内存**: 16GB RAM
- **存储**: 10GB 可用空间（SSD推荐）
- **显卡**: 支持CUDA的NVIDIA显卡（可选）

## 功能特性

### AI问答功能
- ✅ 基于Qwen-1.5-1.8B模型的智能问答
- ✅ 支持中文制度文档理解
- ✅ 上下文相关的准确回答
- ✅ 自动引用来源文档

### 制度检索功能
- ✅ 向量语义搜索
- ✅ 全文关键词搜索
- ✅ 混合搜索结果排序
- ✅ 多格式文档支持

### 文档预览功能
- ✅ PDF.js在线预览
- ✅ 精准定位到相关段落
- ✅ 高亮显示匹配内容
- ✅ 支持缩放和导航

### 向量数据库
- ✅ ChromaDB持久化存储
- ✅ 预构建向量索引
- ✅ 增量更新支持
- ✅ 高效相似度搜索

## 故障排除

### 常见问题

1. **启动失败**
   ```
   问题：双击启动器无反应
   解决：检查Python环境，运行 python --version
   ```

2. **模型加载失败**
   ```
   问题：AI功能不可用
   解决：检查models目录，确保模型文件完整
   ```

3. **内存不足**
   ```
   问题：程序运行缓慢或崩溃
   解决：关闭其他程序，释放内存
   ```

4. **权限问题**
   ```
   问题：无法写入数据文件
   解决：以管理员身份运行，或修改目录权限
   ```

### 日志查看

查看详细错误信息：
```
app/logs/app.log
```

### 技术支持

如遇到问题，请提供：
1. 错误截图
2. 日志文件 (app/logs/app.log)
3. 系统信息 (Windows版本、内存大小)
4. 操作步骤

## 更新维护

### 模型更新
1. 替换 `app/models/` 目录下的模型文件
2. 重启应用程序

### 数据更新
1. 更新 `app/docs/` 目录下的制度文档
2. 运行 `python app/preprocess.py` 重建索引

### 应用更新
1. 备份用户数据
2. 安装新版本
3. 恢复用户数据

## 许可证

本软件遵循公司内部使用许可证，仅供内部使用。

---

**构建时间**: 2025-07-01  
**版本**: v1.0.0  
**技术支持**: 技术部
