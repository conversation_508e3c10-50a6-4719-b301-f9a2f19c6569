#!/usr/bin/env python3
"""
公司制度查询平台 - 完整构建脚本
一键构建零配置安装包
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path
import time

class CompleteBuild:
    """完整构建器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.build_dir = self.project_root / "build_package"
        self.dist_dir = self.project_root / "dist"
        self.nsis_path = r"D:\software\NSIS\makensis.exe"  # 用户的NSIS路径
        
    def check_prerequisites(self):
        """检查构建前提条件"""
        print("🔍 检查构建前提条件...")
        
        # 检查Python版本
        if sys.version_info < (3, 8):
            print("❌ Python版本过低，需要3.8+")
            return False
        
        # 检查NSIS
        if not Path(self.nsis_path).exists():
            print(f"❌ NSIS未找到: {self.nsis_path}")
            print("请安装NSIS或修改nsis_path变量")
            return False
        
        # 检查必要文件
        required_files = [
            "main.py",
            "config.yaml", 
            "requirements.txt",
            "src/",
            "models/",
            "data/"
        ]
        
        for file_path in required_files:
            if not (self.project_root / file_path).exists():
                print(f"❌ 缺少必要文件: {file_path}")
                return False
        
        print("✅ 前提条件检查通过")
        return True
    
    def run_package_builder(self):
        """运行打包构建器"""
        print("📦 运行打包构建器...")
        
        try:
            result = subprocess.run([
                sys.executable, "build_package.py"
            ], check=True, capture_output=True, text=True)
            
            print("✅ 打包构建完成")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 打包构建失败: {e}")
            print(f"错误输出: {e.stderr}")
            return False
    
    def run_config_optimizer(self):
        """运行配置优化器"""
        print("⚙️ 运行配置优化器...")
        
        try:
            result = subprocess.run([
                sys.executable, "optimize_config.py", str(self.build_dir)
            ], check=True, capture_output=True, text=True)
            
            print("✅ 配置优化完成")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 配置优化失败: {e}")
            print(f"错误输出: {e.stderr}")
            return False
    
    def create_launcher_executable(self):
        """创建启动器可执行文件"""
        print("🚀 创建启动器可执行文件...")
        
        try:
            # 运行启动器创建脚本
            subprocess.run([
                sys.executable, "create_launcher.py"
            ], check=True)
            
            # 复制启动器到构建目录
            launcher_files = [
                "smart_launcher.py",
                "start_app.bat"
            ]
            
            for file_name in launcher_files:
                src_file = self.project_root / file_name
                if src_file.exists():
                    shutil.copy2(src_file, self.build_dir / file_name)
            
            print("✅ 启动器创建完成")
            return True
            
        except Exception as e:
            print(f"❌ 启动器创建失败: {e}")
            return False
    
    def create_installer(self):
        """创建NSIS安装器"""
        print("💿 创建NSIS安装器...")
        
        try:
            # 确保dist目录存在
            self.dist_dir.mkdir(exist_ok=True)
            
            # 运行NSIS编译
            nsi_file = self.project_root / "installer.nsi"
            
            result = subprocess.run([
                self.nsis_path, str(nsi_file)
            ], check=True, capture_output=True, text=True, cwd=str(self.project_root))
            
            print("✅ NSIS安装器创建完成")
            
            # 查找生成的安装器
            installer_files = list(self.project_root.glob("CompanyPolicyQA_Setup_*.exe"))
            if installer_files:
                installer_file = installer_files[0]
                print(f"📦 安装器位置: {installer_file}")
                
                # 移动到dist目录
                final_installer = self.dist_dir / installer_file.name
                shutil.move(installer_file, final_installer)
                print(f"📦 最终安装器: {final_installer}")
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ NSIS编译失败: {e}")
            print(f"错误输出: {e.stderr}")
            return False
    
    def create_portable_package(self):
        """创建便携版打包"""
        print("📁 创建便携版打包...")
        
        try:
            portable_dir = self.dist_dir / "CompanyPolicyQA_Portable"
            
            # 复制构建结果
            if portable_dir.exists():
                shutil.rmtree(portable_dir)
            
            shutil.copytree(self.build_dir, portable_dir)
            
            # 创建便携版说明
            readme_content = '''# 公司制度查询平台 - 便携版

## 使用说明

1. 解压到任意目录
2. 双击 start_app.bat 启动程序
3. 首次运行会自动初始化配置

## 系统要求

- Windows 10/11 (64位)
- 内存: 8GB以上推荐
- 磁盘空间: 5GB以上

## 功能特性

- ✅ AI智能问答
- ✅ 制度文档检索
- ✅ 向量语义搜索
- ✅ 文档预览定位
- ✅ 零配置运行

## 技术支持

如有问题请联系技术支持团队。
'''
            
            readme_file = portable_dir / "README.md"
            readme_file.write_text(readme_content, encoding='utf-8')
            
            # 创建便携版压缩包
            portable_zip = self.dist_dir / "CompanyPolicyQA_Portable_v1.0.0.zip"
            shutil.make_archive(
                str(portable_zip.with_suffix('')),
                'zip',
                str(portable_dir)
            )
            
            print(f"✅ 便携版创建完成: {portable_zip}")
            return True
            
        except Exception as e:
            print(f"❌ 便携版创建失败: {e}")
            return False
    
    def generate_build_report(self):
        """生成构建报告"""
        print("📊 生成构建报告...")
        
        try:
            # 计算大小
            def get_size_mb(path):
                total_size = 0
                for file_path in Path(path).rglob("*"):
                    if file_path.is_file():
                        total_size += file_path.stat().st_size
                return total_size / (1024 * 1024)
            
            build_size = get_size_mb(self.build_dir) if self.build_dir.exists() else 0
            
            # 生成报告
            report = f'''# 构建报告

## 构建信息
- 构建时间: {time.strftime("%Y-%m-%d %H:%M:%S")}
- Python版本: {sys.version}
- 构建目录: {self.build_dir}
- 输出目录: {self.dist_dir}

## 包大小
- 构建包大小: {build_size:.1f} MB

## 输出文件
'''
            
            # 列出输出文件
            if self.dist_dir.exists():
                for file_path in self.dist_dir.iterdir():
                    if file_path.is_file():
                        size_mb = file_path.stat().st_size / (1024 * 1024)
                        report += f"- {file_path.name}: {size_mb:.1f} MB\\n"
            
            report += '''
## 部署说明

### 安装器版本
1. 运行 CompanyPolicyQA_Setup_v1.0.0.exe
2. 按照向导完成安装
3. 启动程序开始使用

### 便携版本
1. 解压 CompanyPolicyQA_Portable_v1.0.0.zip
2. 运行 start_app.bat
3. 等待初始化完成

## 注意事项
- 首次运行需要联网下载模型（如果本地没有）
- 建议在8GB以上内存的机器上运行
- 支持Windows 10/11 64位系统
'''
            
            report_file = self.dist_dir / "BUILD_REPORT.md"
            report_file.write_text(report, encoding='utf-8')
            
            print(f"✅ 构建报告已生成: {report_file}")
            return True
            
        except Exception as e:
            print(f"❌ 构建报告生成失败: {e}")
            return False
    
    def build_all(self):
        """执行完整构建"""
        print("🏗️ 开始完整构建...")
        print("=" * 60)
        
        start_time = time.time()
        
        try:
            # 1. 检查前提条件
            if not self.check_prerequisites():
                return False
            
            # 2. 运行打包构建器
            if not self.run_package_builder():
                return False
            
            # 3. 运行配置优化器
            if not self.run_config_optimizer():
                return False
            
            # 4. 创建启动器
            if not self.create_launcher_executable():
                return False
            
            # 5. 创建便携版
            if not self.create_portable_package():
                return False
            
            # 6. 创建安装器
            if not self.create_installer():
                return False
            
            # 7. 生成构建报告
            if not self.generate_build_report():
                return False
            
            build_time = time.time() - start_time
            
            print("=" * 60)
            print(f"🎉 构建完成！耗时: {build_time:.1f}秒")
            print(f"📦 输出目录: {self.dist_dir}")
            print("=" * 60)
            
            return True
            
        except Exception as e:
            print(f"❌ 构建过程中发生错误: {e}")
            return False

def main():
    """主函数"""
    builder = CompleteBuild()
    success = builder.build_all()
    
    if success:
        print("\\n🚀 构建成功！可以开始部署了。")
    else:
        print("\\n❌ 构建失败，请检查错误信息。")
    
    input("\\n按回车键退出...")
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
