; 公司制度查询平台 NSIS 安装脚本
; 支持零配置部署和增量更新

!define APP_NAME "公司制度查询平台"
!define APP_VERSION "1.0.0"
!define APP_PUBLISHER "公司名称"
!define APP_URL "http://www.company.com"
!define APP_EXECUTABLE "launcher.exe"
!define APP_UNINSTALLER "uninstall.exe"

; 安装目录
!define DEFAULT_INSTALL_DIR "$PROGRAMFILES64\CompanyPolicyQA"

; 包含现代UI
!include "MUI2.nsh"
!include "FileFunc.nsh"
!include "LogicLib.nsh"

; 安装器属性
Name "${APP_NAME}"
OutFile "CompanyPolicyQA_Setup_v${APP_VERSION}.exe"
InstallDir "${DEFAULT_INSTALL_DIR}"
InstallDirRegKey HKLM "Software\${APP_NAME}" "InstallDir"
RequestExecutionLevel admin

; 界面设置
!define MUI_ABORTWARNING
!define MUI_ICON "app\static\icon.ico"
!define MUI_UNICON "app\static\icon.ico"
!define MUI_HEADERIMAGE
!define MUI_HEADERIMAGE_BITMAP "installer_header.bmp"
!define MUI_WELCOMEFINISHPAGE_BITMAP "installer_welcome.bmp"

; 安装页面
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE.txt"
!insertmacro MUI_PAGE_COMPONENTS
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!define MUI_FINISHPAGE_RUN "$INSTDIR\${APP_EXECUTABLE}"
!define MUI_FINISHPAGE_RUN_TEXT "启动 ${APP_NAME}"
!insertmacro MUI_PAGE_FINISH

; 卸载页面
!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; 语言
!insertmacro MUI_LANGUAGE "SimpChinese"

; 版本信息
VIProductVersion "${APP_VERSION}.0"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "ProductName" "${APP_NAME}"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "CompanyName" "${APP_PUBLISHER}"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "FileVersion" "${APP_VERSION}"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "ProductVersion" "${APP_VERSION}"
VIAddVersionKey /LANG=${LANG_SIMPCHINESE} "FileDescription" "${APP_NAME} 安装程序"

; 安装组件
Section "核心程序" SecCore
    SectionIn RO  ; 必需组件
    
    SetOutPath "$INSTDIR"
    
    ; 检查磁盘空间 (至少需要5GB)
    ${GetRoot} "$INSTDIR" $R0
    ${DriveSpace} "$R0" "/D=F /S=M" $R1
    ${If} $R1 < 5120  ; 5GB = 5120MB
        MessageBox MB_OK|MB_ICONSTOP "磁盘空间不足！至少需要5GB可用空间。"
        Abort
    ${EndIf}
    
    ; 复制文件
    DetailPrint "正在安装核心程序..."
    File /r "build_package\*"
    
    ; 创建启动器
    DetailPrint "正在创建启动器..."
    CreateShortCut "$INSTDIR\${APP_EXECUTABLE}" "$INSTDIR\launcher.py"
    
    ; 写入注册表
    WriteRegStr HKLM "Software\${APP_NAME}" "InstallDir" "$INSTDIR"
    WriteRegStr HKLM "Software\${APP_NAME}" "Version" "${APP_VERSION}"
    
    ; 创建卸载器
    WriteUninstaller "$INSTDIR\${APP_UNINSTALLER}"
    
    ; 添加到控制面板
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "DisplayName" "${APP_NAME}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "UninstallString" "$INSTDIR\${APP_UNINSTALLER}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "DisplayIcon" "$INSTDIR\app\static\icon.ico"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "Publisher" "${APP_PUBLISHER}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "DisplayVersion" "${APP_VERSION}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "URLInfoAbout" "${APP_URL}"
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "NoModify" 1
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "NoRepair" 1
    
    ; 计算安装大小
    ${GetSize} "$INSTDIR" "/S=0K" $0 $1 $2
    IntFmt $0 "0x%08X" $0
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}" "EstimatedSize" "$0"
    
SectionEnd

Section "桌面快捷方式" SecDesktop
    CreateShortCut "$DESKTOP\${APP_NAME}.lnk" "$INSTDIR\${APP_EXECUTABLE}" "" "$INSTDIR\app\static\icon.ico"
SectionEnd

Section "开始菜单快捷方式" SecStartMenu
    CreateDirectory "$SMPROGRAMS\${APP_NAME}"
    CreateShortCut "$SMPROGRAMS\${APP_NAME}\${APP_NAME}.lnk" "$INSTDIR\${APP_EXECUTABLE}" "" "$INSTDIR\app\static\icon.ico"
    CreateShortCut "$SMPROGRAMS\${APP_NAME}\卸载 ${APP_NAME}.lnk" "$INSTDIR\${APP_UNINSTALLER}"
SectionEnd

Section "文件关联" SecFileAssoc
    ; 关联.policy文件类型
    WriteRegStr HKCR ".policy" "" "PolicyDocument"
    WriteRegStr HKCR "PolicyDocument" "" "制度文档"
    WriteRegStr HKCR "PolicyDocument\DefaultIcon" "" "$INSTDIR\app\static\icon.ico"
    WriteRegStr HKCR "PolicyDocument\shell\open\command" "" '"$INSTDIR\${APP_EXECUTABLE}" "%1"'
SectionEnd

; 组件描述
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
    !insertmacro MUI_DESCRIPTION_TEXT ${SecCore} "安装 ${APP_NAME} 核心程序文件（必需）"
    !insertmacro MUI_DESCRIPTION_TEXT ${SecDesktop} "在桌面创建快捷方式"
    !insertmacro MUI_DESCRIPTION_TEXT ${SecStartMenu} "在开始菜单创建快捷方式"
    !insertmacro MUI_DESCRIPTION_TEXT ${SecFileAssoc} "关联制度文档文件类型"
!insertmacro MUI_FUNCTION_DESCRIPTION_END

; 安装前检查
Function .onInit
    ; 检查是否已安装
    ReadRegStr $R0 HKLM "Software\${APP_NAME}" "InstallDir"
    ${If} $R0 != ""
        MessageBox MB_YESNO|MB_ICONQUESTION "检测到已安装 ${APP_NAME}。是否要升级安装？" IDYES upgrade IDNO abort
        upgrade:
            ; 继续安装（升级模式）
            Goto done
        abort:
            Abort
        done:
    ${EndIf}
    
    ; 检查系统要求
    ${If} ${RunningX64}
        ; 64位系统
    ${Else}
        MessageBox MB_OK|MB_ICONSTOP "此程序需要64位Windows系统。"
        Abort
    ${EndIf}
    
    ; 检查内存
    System::Call "kernel32::GlobalMemoryStatusEx(*i) i (r1) .r2"
    System::Call "*$1(i 64, *l .r3, *l, *l, *l, *l, *l, *l, *l)"
    IntOp $3 $3 / 1073741824  ; 转换为GB
    ${If} $3 < 8
        MessageBox MB_YESNO|MB_ICONEXCLAMATION "系统内存少于8GB，可能影响程序性能。是否继续安装？" IDYES continue IDNO abort
        continue:
        abort:
            Abort
    ${EndIf}
FunctionEnd

; 安装后处理
Function .onInstSuccess
    ; 设置文件权限
    AccessControl::GrantOnFile "$INSTDIR" "(BU)" "FullAccess"
    
    ; 创建数据目录
    CreateDirectory "$INSTDIR\app\data\sessions"
    CreateDirectory "$INSTDIR\app\logs"
    
    ; 初始化配置
    ExecWait '"$INSTDIR\python\python.exe" "$INSTDIR\app\preprocess.py" --init-only'
    
    MessageBox MB_OK "安装完成！${APP_NAME} 已成功安装到您的计算机。"
FunctionEnd

; 卸载程序
Section "Uninstall"
    ; 停止程序
    DetailPrint "正在停止程序..."
    KillProcDLL::KillProc "launcher.exe"
    KillProcDLL::KillProc "python.exe"
    
    ; 删除文件
    DetailPrint "正在删除程序文件..."
    RMDir /r "$INSTDIR\python"
    RMDir /r "$INSTDIR\app"
    Delete "$INSTDIR\launcher.py"
    Delete "$INSTDIR\${APP_UNINSTALLER}"
    
    ; 删除快捷方式
    Delete "$DESKTOP\${APP_NAME}.lnk"
    RMDir /r "$SMPROGRAMS\${APP_NAME}"
    
    ; 删除注册表项
    DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_NAME}"
    DeleteRegKey HKLM "Software\${APP_NAME}"
    DeleteRegKey HKCR ".policy"
    DeleteRegKey HKCR "PolicyDocument"
    
    ; 删除安装目录
    RMDir "$INSTDIR"
    
    MessageBox MB_OK "${APP_NAME} 已成功卸载。"
SectionEnd

; 卸载前确认
Function un.onInit
    MessageBox MB_YESNO|MB_ICONQUESTION "确定要完全删除 ${APP_NAME} 及其所有组件吗？" IDYES +2
    Abort
FunctionEnd
