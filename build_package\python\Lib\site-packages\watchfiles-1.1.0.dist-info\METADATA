Metadata-Version: 2.4
Name: watchfiles
Version: 1.1.0
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: 3.14
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Information Technology
Classifier: Intended Audience :: System Administrators
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: MacOS
Classifier: Environment :: MacOS X
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Topic :: System :: Filesystems
Classifier: Framework :: AnyIO
Requires-Dist: anyio>=3.0.0
License-File: LICENSE
Summary: Simple, modern and high performance file watching and code reload in python.
Home-Page: https://github.com/samuelcolvin/watchfiles
Author-email: <PERSON> <<EMAIL>>
License: MIT
Requires-Python: >=3.9
Description-Content-Type: text/markdown; charset=UTF-8; variant=GFM
Project-URL: Homepage, https://github.com/samuelcolvin/watchfiles
Project-URL: Documentation, https://watchfiles.helpmanual.io
Project-URL: Funding, https://github.com/sponsors/samuelcolvin
Project-URL: Source, https://github.com/samuelcolvin/watchfiles
Project-URL: Changelog, https://github.com/samuelcolvin/watchfiles/releases

# watchfiles

[![CI](https://github.com/samuelcolvin/watchfiles/actions/workflows/ci.yml/badge.svg)](https://github.com/samuelcolvin/watchfiles/actions/workflows/ci.yml?query=branch%3Amain)
[![Coverage](https://codecov.io/gh/samuelcolvin/watchfiles/branch/main/graph/badge.svg)](https://codecov.io/gh/samuelcolvin/watchfiles)
[![pypi](https://img.shields.io/pypi/v/watchfiles.svg)](https://pypi.python.org/pypi/watchfiles)
[![CondaForge](https://img.shields.io/conda/v/conda-forge/watchfiles.svg)](https://anaconda.org/conda-forge/watchfiles)
[![license](https://img.shields.io/github/license/samuelcolvin/watchfiles.svg)](https://github.com/samuelcolvin/watchfiles/blob/main/LICENSE)

Simple, modern and high performance file watching and code reload in python.

---

**Documentation**: [watchfiles.helpmanual.io](https://watchfiles.helpmanual.io)

**Source Code**: [github.com/samuelcolvin/watchfiles](https://github.com/samuelcolvin/watchfiles)

---

Underlying file system notifications are handled by the [Notify](https://github.com/notify-rs/notify) rust library.

This package was previously named "watchgod",
see [the migration guide](https://watchfiles.helpmanual.io/migrating/) for more information.

## Installation

**watchfiles** requires Python 3.9 - 3.14.

```bash
pip install watchfiles
```

Binaries are available for most architectures on Linux, MacOS and Windows ([learn more](https://watchfiles.helpmanual.io/#installation)).

Otherwise, you can install from source which requires Rust stable to be installed.

## Usage

Here are some examples of what **watchfiles** can do:

### `watch` Usage

```py
from watchfiles import watch

for changes in watch('./path/to/dir'):
    print(changes)
```
See [`watch` docs](https://watchfiles.helpmanual.io/api/watch/#watchfiles.watch) for more details.

### `awatch` Usage

```py
import asyncio
from watchfiles import awatch

async def main():
    async for changes in awatch('/path/to/dir'):
        print(changes)

asyncio.run(main())
```
See [`awatch` docs](https://watchfiles.helpmanual.io/api/watch/#watchfiles.awatch) for more details.

### `run_process` Usage

```py
from watchfiles import run_process

def foobar(a, b, c):
    ...

if __name__ == '__main__':
    run_process('./path/to/dir', target=foobar, args=(1, 2, 3))
```
See [`run_process` docs](https://watchfiles.helpmanual.io/api/run_process/#watchfiles.run_process) for more details.

### `arun_process` Usage

```py
import asyncio
from watchfiles import arun_process

def foobar(a, b, c):
    ...

async def main():
    await arun_process('./path/to/dir', target=foobar, args=(1, 2, 3))

if __name__ == '__main__':
    asyncio.run(main())
```
See [`arun_process` docs](https://watchfiles.helpmanual.io/api/run_process/#watchfiles.arun_process) for more details.

## CLI

**watchfiles** also comes with a CLI for running and reloading code. To run `some command` when files in `src` change:

```
watchfiles "some command" src
```

For more information, see [the CLI docs](https://watchfiles.helpmanual.io/cli/).

Or run

```bash
watchfiles --help
```

