#!/usr/bin/env python3
"""
配置优化脚本
为打包环境优化配置文件和路径设置
"""
import os
import sys
import yaml
import json
from pathlib import Path
from typing import Dict, Any

class ConfigOptimizer:
    """配置优化器"""
    
    def __init__(self, build_dir: Path):
        self.build_dir = Path(build_dir)
        self.app_dir = self.build_dir / "app"
        self.config_file = self.app_dir / "config.yaml"
    
    def optimize_config_yaml(self):
        """优化config.yaml配置"""
        print("⚙️ 优化配置文件...")
        
        if not self.config_file.exists():
            print(f"❌ 配置文件不存在: {self.config_file}")
            return False
        
        # 读取配置
        with open(self.config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 优化路径配置 - 使用相对路径
        config.update({
            'docs_dir': 'docs',
            'data_dir': 'data', 
            'models_dir': 'models',
            'preview_dir': 'data/preview_pdfs'
        })
        
        # 优化模型配置
        if 'model' in config:
            # 确保使用本地模型
            config['model']['embedding_model'] = 'shibing624/text2vec-base-chinese'
            config['model']['llm_model'] = 'qwen-1.5-1.8b-chat'
            # 降低内存使用
            config['model']['max_tokens'] = 512
            config['model']['device'] = 'auto'
        
        # 优化数据库配置
        if 'database' in config:
            config['database']['chroma_persist_dir'] = 'data/chroma_db'
            config['database']['whoosh_index_dir'] = 'data/whoosh_index'
        
        # 优化性能配置
        config.update({
            'max_memory_gb': 6,  # 降低内存要求
            'enable_cache': True,
            'cache_size': 500,   # 减少缓存大小
            'log_level': 'INFO',
            'log_file': 'logs/app.log'
        })
        
        # 保存优化后的配置
        with open(self.config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, 
                     allow_unicode=True, indent=2)
        
        print("✅ 配置文件优化完成")
        return True
    
    def create_path_helper(self):
        """创建路径辅助模块"""
        print("📁 创建路径辅助模块...")
        
        path_helper_content = '''"""
打包环境路径辅助模块
自动处理绝对路径和相对路径转换
"""
import os
import sys
from pathlib import Path

def get_app_root():
    """获取应用根目录"""
    if getattr(sys, 'frozen', False):
        # 打包环境
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller
            return Path(sys._MEIPASS)
        else:
            # 其他打包方式
            return Path(sys.executable).parent / "app"
    else:
        # 开发环境
        return Path(__file__).parent.parent

def get_resource_path(relative_path):
    """获取资源文件路径"""
    app_root = get_app_root()
    return app_root / relative_path

def get_data_path(relative_path=""):
    """获取数据目录路径"""
    app_root = get_app_root()
    data_dir = app_root / "data"
    data_dir.mkdir(exist_ok=True)
    return data_dir / relative_path if relative_path else data_dir

def get_models_path(relative_path=""):
    """获取模型目录路径"""
    app_root = get_app_root()
    models_dir = app_root / "models"
    models_dir.mkdir(exist_ok=True)
    return models_dir / relative_path if relative_path else models_dir

def get_docs_path(relative_path=""):
    """获取文档目录路径"""
    app_root = get_app_root()
    docs_dir = app_root / "docs"
    docs_dir.mkdir(exist_ok=True)
    return docs_dir / relative_path if relative_path else docs_dir

def get_logs_path(relative_path=""):
    """获取日志目录路径"""
    app_root = get_app_root()
    logs_dir = app_root / "logs"
    logs_dir.mkdir(exist_ok=True)
    return logs_dir / relative_path if relative_path else logs_dir

def ensure_dir(path):
    """确保目录存在"""
    Path(path).mkdir(parents=True, exist_ok=True)
    return path

# 环境变量设置
def setup_environment():
    """设置环境变量"""
    app_root = get_app_root()
    
    # 设置应用路径
    os.environ["APP_ROOT"] = str(app_root)
    os.environ["PYTHONPATH"] = str(app_root)
    
    # 设置数据路径
    os.environ["DATA_DIR"] = str(get_data_path())
    os.environ["MODELS_DIR"] = str(get_models_path())
    os.environ["DOCS_DIR"] = str(get_docs_path())
    os.environ["LOGS_DIR"] = str(get_logs_path())

# 自动设置环境
setup_environment()
'''
        
        path_helper_file = self.app_dir / "src" / "utils" / "path_helper.py"
        path_helper_file.parent.mkdir(parents=True, exist_ok=True)
        path_helper_file.write_text(path_helper_content, encoding='utf-8')
        
        print("✅ 路径辅助模块创建完成")
    
    def patch_config_module(self):
        """修补配置模块以支持打包环境"""
        print("🔧 修补配置模块...")
        
        config_module = self.app_dir / "src" / "utils" / "config.py"
        if not config_module.exists():
            print(f"❌ 配置模块不存在: {config_module}")
            return False
        
        # 读取原始配置模块
        content = config_module.read_text(encoding='utf-8')
        
        # 添加路径辅助导入
        if "from .path_helper import" not in content:
            import_line = "from .path_helper import get_app_root, get_resource_path\n"
            # 在第一个import后添加
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if line.startswith('import ') or line.startswith('from '):
                    lines.insert(i + 1, import_line.strip())
                    break
            content = '\n'.join(lines)
        
        # 修改base_dir获取方式
        if "self.base_dir = Path.cwd()" in content:
            content = content.replace(
                "self.base_dir = Path.cwd()",
                "self.base_dir = get_app_root()"
            )
        
        # 保存修改后的配置模块
        config_module.write_text(content, encoding='utf-8')
        
        print("✅ 配置模块修补完成")
        return True
    
    def create_startup_script(self):
        """创建启动脚本"""
        print("🚀 创建启动脚本...")
        
        startup_content = '''#!/usr/bin/env python3
"""
打包环境启动脚本
"""
import os
import sys
from pathlib import Path

# 添加应用目录到Python路径
app_dir = Path(__file__).parent
sys.path.insert(0, str(app_dir))

# 导入路径辅助
from src.utils.path_helper import setup_environment

# 设置环境
setup_environment()

# 启动主程序
if __name__ == "__main__":
    from main import main
    main()
'''
        
        startup_file = self.app_dir / "startup.py"
        startup_file.write_text(startup_content, encoding='utf-8')
        
        print("✅ 启动脚本创建完成")
    
    def optimize_dependencies(self):
        """优化依赖配置"""
        print("📦 优化依赖配置...")
        
        # 创建依赖检查跳过文件
        skip_check_content = '''"""
打包环境依赖检查跳过标记
"""
# 此文件存在时，跳过依赖检查
PACKAGED_ENVIRONMENT = True
'''
        
        skip_file = self.app_dir / "src" / "utils" / "packaged.py"
        skip_file.write_text(skip_check_content, encoding='utf-8')
        
        print("✅ 依赖配置优化完成")
    
    def create_version_info(self):
        """创建版本信息"""
        print("ℹ️ 创建版本信息...")
        
        version_info = {
            "app_name": "公司制度查询平台",
            "version": "1.0.0",
            "build_date": "2025-07-01",
            "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            "packaged": True,
            "features": [
                "AI问答",
                "制度检索", 
                "文档预览",
                "向量搜索"
            ]
        }
        
        version_file = self.app_dir / "version.json"
        with open(version_file, 'w', encoding='utf-8') as f:
            json.dump(version_info, f, ensure_ascii=False, indent=2)
        
        print("✅ 版本信息创建完成")
    
    def optimize_all(self):
        """执行所有优化"""
        print("🔧 开始配置优化...")
        
        try:
            self.optimize_config_yaml()
            self.create_path_helper()
            self.patch_config_module()
            self.create_startup_script()
            self.optimize_dependencies()
            self.create_version_info()
            
            print("✅ 所有配置优化完成！")
            return True
            
        except Exception as e:
            print(f"❌ 配置优化失败: {e}")
            return False

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python optimize_config.py <build_dir>")
        sys.exit(1)
    
    build_dir = Path(sys.argv[1])
    if not build_dir.exists():
        print(f"❌ 构建目录不存在: {build_dir}")
        sys.exit(1)
    
    optimizer = ConfigOptimizer(build_dir)
    success = optimizer.optimize_all()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
