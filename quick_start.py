#!/usr/bin/env python3
"""
公司制度查询平台 - 快速开始脚本
一键检查环境并开始打包
"""
import os
import sys
import subprocess
import platform
from pathlib import Path

def print_banner():
    """打印横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    公司制度查询平台                          ║
║                  零配置打包解决方案                          ║
║                                                              ║
║  🎯 目标：实现AI问答、制度检索、文档预览的零配置交付         ║
║  🚀 特性：嵌入式Python + 预编译依赖 + 智能启动器            ║
║  📦 输出：NSIS安装器 + 便携版压缩包                         ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def check_system():
    """检查系统环境"""
    print("🔍 检查系统环境...")
    
    # 检查操作系统
    if platform.system() != "Windows":
        print("❌ 此打包方案仅支持Windows系统")
        return False
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print(f"❌ Python版本过低: {sys.version}")
        print("   需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查内存
    try:
        import psutil
        memory_gb = psutil.virtual_memory().total / (1024**3)
        if memory_gb < 8:
            print(f"⚠️ 系统内存较低: {memory_gb:.1f}GB (推荐8GB+)")
        else:
            print(f"✅ 系统内存: {memory_gb:.1f}GB")
    except ImportError:
        print("⚠️ 无法检查内存信息 (psutil未安装)")
    
    # 检查磁盘空间
    try:
        import shutil
        free_space_gb = shutil.disk_usage(".").free / (1024**3)
        if free_space_gb < 10:
            print(f"⚠️ 磁盘空间不足: {free_space_gb:.1f}GB (推荐10GB+)")
        else:
            print(f"✅ 可用磁盘空间: {free_space_gb:.1f}GB")
    except:
        print("⚠️ 无法检查磁盘空间")
    
    return True

def check_project_structure():
    """检查项目结构"""
    print("📁 检查项目结构...")
    
    required_files = [
        "main.py",
        "config.yaml",
        "requirements.txt",
        "src/",
        "models/",
        "data/"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要文件/目录:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    print("✅ 项目结构完整")
    return True

def check_dependencies():
    """检查Python依赖"""
    print("📦 检查Python依赖...")

    # 包名映射：导入名 -> 显示名
    critical_packages = {
        "torch": "torch",
        "transformers": "transformers",
        "sentence_transformers": "sentence-transformers",  # 注意下划线
        "chromadb": "chromadb",
        "PyQt6": "PyQt6",
        "yaml": "yaml",
        "requests": "requests"
    }

    missing_packages = []
    for import_name, display_name in critical_packages.items():
        try:
            __import__(import_name)
            print(f"✅ {display_name}")
        except ImportError:
            missing_packages.append(display_name)
            print(f"❌ {display_name}")

    if missing_packages:
        print(f"\n⚠️ 缺少 {len(missing_packages)} 个关键依赖包")
        print("建议运行: pip install -r requirements.txt")
        return False

    print("✅ 所有关键依赖已安装")
    return True

def check_nsis():
    """检查NSIS安装"""
    print("🔧 检查NSIS安装...")
    
    nsis_paths = [
        r"D:\software\NSIS\makensis.exe",  # 用户指定路径
        r"C:\Program Files (x86)\NSIS\makensis.exe",
        r"C:\Program Files\NSIS\makensis.exe"
    ]
    
    for nsis_path in nsis_paths:
        if Path(nsis_path).exists():
            print(f"✅ NSIS找到: {nsis_path}")
            return True
    
    print("❌ 未找到NSIS安装")
    print("请从 https://nsis.sourceforge.io/ 下载并安装NSIS")
    print("或修改 build_complete.py 中的 nsis_path 变量")
    return False

def check_models():
    """检查AI模型"""
    print("🤖 检查AI模型...")
    
    models_dir = Path("models")
    if not models_dir.exists():
        print("❌ models目录不存在")
        return False
    
    required_models = [
        "qwen-1.5-1.8b-chat",
        "shibing624"
    ]
    
    missing_models = []
    for model_name in required_models:
        model_path = models_dir / model_name
        if not model_path.exists() or not any(model_path.iterdir()):
            missing_models.append(model_name)
    
    if missing_models:
        print("⚠️ 缺少AI模型:")
        for model in missing_models:
            print(f"   - {model}")
        print("首次运行时会自动下载，但建议预先准备")
    else:
        print("✅ AI模型完整")
    
    return True

def estimate_build_time():
    """估算构建时间"""
    print("⏱️ 构建时间估算...")
    
    # 基于不同因素估算时间
    base_time = 10  # 基础时间（分钟）
    
    # 网络下载时间
    models_dir = Path("models")
    if not models_dir.exists() or not any(models_dir.iterdir()):
        base_time += 30  # 下载模型需要额外时间
        print("   📥 需要下载AI模型: +30分钟")
    
    # Python环境下载
    python_cache = Path("build_package/python")
    if not python_cache.exists():
        base_time += 5  # 下载Python环境
        print("   🐍 需要下载Python环境: +5分钟")
    
    # 依赖安装时间
    base_time += 15  # 安装依赖包
    print("   📦 安装依赖包: +15分钟")
    
    print(f"   ⏱️ 预计总时间: {base_time}分钟")
    return base_time

def show_menu():
    """显示菜单"""
    print("\n" + "="*60)
    print("请选择操作:")
    print("1. 🚀 开始完整构建 (推荐)")
    print("2. 🔧 仅检查环境")
    print("3. 📦 分步构建")
    print("4. 🧪 测试现有构建")
    print("5. 📖 查看帮助文档")
    print("0. 退出")
    print("="*60)
    
    while True:
        try:
            choice = input("请输入选择 (0-5): ").strip()
            if choice in ['0', '1', '2', '3', '4', '5']:
                return choice
            else:
                print("❌ 无效选择，请输入0-5")
        except KeyboardInterrupt:
            print("\n👋 用户中断")
            return '0'

def run_full_build():
    """运行完整构建"""
    print("\n🚀 开始完整构建...")
    
    try:
        result = subprocess.run([
            sys.executable, "build_complete.py"
        ], check=True)
        
        print("✅ 构建完成！")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return False
    except FileNotFoundError:
        print("❌ 找不到 build_complete.py 文件")
        return False

def run_step_build():
    """分步构建"""
    print("\n🔧 分步构建模式...")
    
    steps = [
        ("基础打包", "build_package.py"),
        ("配置优化", "optimize_config.py build_package"),
        ("创建启动器", "create_launcher.py"),
        ("测试验证", "test_package.py build_package")
    ]
    
    for step_name, command in steps:
        print(f"\n📋 执行: {step_name}")
        confirm = input(f"是否执行 '{command}'? (y/n): ").strip().lower()
        
        if confirm in ['y', 'yes', '是']:
            try:
                if ' ' in command:
                    cmd_parts = command.split()
                    subprocess.run([sys.executable] + cmd_parts, check=True)
                else:
                    subprocess.run([sys.executable, command], check=True)
                print(f"✅ {step_name} 完成")
            except subprocess.CalledProcessError as e:
                print(f"❌ {step_name} 失败: {e}")
                if input("是否继续下一步? (y/n): ").strip().lower() not in ['y', 'yes', '是']:
                    break
        else:
            print(f"⏭️ 跳过 {step_name}")

def test_existing_build():
    """测试现有构建"""
    print("\n🧪 测试现有构建...")
    
    build_dir = Path("build_package")
    if not build_dir.exists():
        print("❌ 构建目录不存在，请先运行构建")
        return False
    
    try:
        result = subprocess.run([
            sys.executable, "test_package.py", str(build_dir)
        ], check=True)
        
        print("✅ 测试完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 测试失败: {e}")
        return False

def show_help():
    """显示帮助"""
    help_text = """
📖 帮助文档

## 快速开始
1. 确保Python 3.8+环境
2. 安装依赖: pip install -r requirements.txt
3. 下载并安装NSIS
4. 运行: python quick_start.py

## 文件说明
- build_complete.py: 完整构建脚本
- build_package.py: 基础打包脚本
- optimize_config.py: 配置优化脚本
- create_launcher.py: 启动器创建脚本
- test_package.py: 测试验证脚本
- installer.nsi: NSIS安装器脚本

## 输出结果
- dist/CompanyPolicyQA_Setup_v1.0.0.exe: 安装器
- dist/CompanyPolicyQA_Portable_v1.0.0.zip: 便携版

## 系统要求
- Windows 10/11 (64位)
- 8GB+ 内存
- 10GB+ 磁盘空间
- NSIS 3.0+

详细文档请查看: PACKAGING_GUIDE.md
"""
    print(help_text)

def main():
    """主函数"""
    print_banner()
    
    # 环境检查
    if not check_system():
        input("\n按回车键退出...")
        return
    
    if not check_project_structure():
        input("\n按回车键退出...")
        return
    
    # 可选检查
    check_dependencies()
    check_nsis()
    check_models()
    estimate_build_time()
    
    # 主菜单循环
    while True:
        choice = show_menu()
        
        if choice == '0':
            print("👋 再见！")
            break
        elif choice == '1':
            run_full_build()
        elif choice == '2':
            print("✅ 环境检查已完成")
        elif choice == '3':
            run_step_build()
        elif choice == '4':
            test_existing_build()
        elif choice == '5':
            show_help()
        
        if choice != '0':
            input("\n按回车键继续...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        input("按回车键退出...")
