from sympy.liealgebras.cartan_type import Standard_Cartan
from sympy.core.backend import eye


class TypeA(Standard_Cartan):
    """
    This class contains the information about
    the A series of simple Lie algebras.
    ====
    """

    def __new__(cls, n):
        if n < 1:
            raise ValueError("n cannot be less than 1")
        return Standard_Cartan.__new__(cls, "A", n)


    def dimension(self):
        """Dimension of the vector space V underlying the Lie algebra

        Examples
        ========

        >>> from sympy.liealgebras.cartan_type import CartanType
        >>> c = CartanType("A4")
        >>> c.dimension()
        5
        """
        return self.n+1


    def basic_root(self, i, j):
        """
        This is a method just to generate roots
        with a 1 iin the ith position and a -1
        in the jth position.

        """

        n = self.n
        root = [0]*(n+1)
        root[i] = 1
        root[j] = -1
        return root

    def simple_root(self, i):
        """
        Every lie algebra has a unique root system.
        Given a root system Q, there is a subset of the
        roots such that an element of Q is called a
        simple root if it cannot be written as the sum
        of two elements in Q.  If we let D denote the
        set of simple roots, then it is clear that every
        element of Q can be written as a linear combination
        of elements of D with all coefficients non-negative.

        In A_n the ith simple root is the root which has a 1
        in the ith position, a -1 in the (i+1)th position,
        and zeroes elsewhere.

        This method returns the ith simple root for the A series.

        Examples
        ========

        >>> from sympy.liealgebras.cartan_type import CartanType
        >>> c = CartanType("A4")
        >>> c.simple_root(1)
        [1, -1, 0, 0, 0]

        """

        return self.basic_root(i-1, i)

    def positive_roots(self):
        """
        This method generates all the positive roots of
        A_n.  This is half of all of the roots of A_n;
        by multiplying all the positive roots by -1 we
        get the negative roots.

        Examples
        ========

        >>> from sympy.liealgebras.cartan_type import CartanType
        >>> c = CartanType("A3")
        >>> c.positive_roots()
        {1: [1, -1, 0, 0], 2: [1, 0, -1, 0], 3: [1, 0, 0, -1], 4: [0, 1, -1, 0],
                5: [0, 1, 0, -1], 6: [0, 0, 1, -1]}
        """

        n = self.n
        posroots = {}
        k = 0
        for i in range(0, n):
            for j in range(i+1, n+1):
               k += 1
               posroots[k] = self.basic_root(i, j)
        return posroots

    def highest_root(self):
        """
        Returns the highest weight root for A_n
        """

        return self.basic_root(0, self.n)

    def roots(self):
        """
        Returns the total number of roots for A_n
        """
        n = self.n
        return n*(n+1)

    def cartan_matrix(self):
        """
        Returns the Cartan matrix for A_n.
        The Cartan matrix matrix for a Lie algebra is
        generated by assigning an ordering to the simple
        roots, (alpha[1], ...., alpha[l]).  Then the ijth
        entry of the Cartan matrix is (<alpha[i],alpha[j]>).

        Examples
        ========

        >>> from sympy.liealgebras.cartan_type import CartanType
        >>> c = CartanType('A4')
        >>> c.cartan_matrix()
        Matrix([
        [ 2, -1,  0,  0],
        [-1,  2, -1,  0],
        [ 0, -1,  2, -1],
        [ 0,  0, -1,  2]])

        """

        n = self.n
        m = 2 * eye(n)
        i = 1
        while i < n-1:
            m[i, i+1] = -1
            m[i, i-1] = -1
            i += 1
        m[0,1] = -1
        m[n-1, n-2] = -1
        return m

    def basis(self):
        """
        Returns the number of independent generators of A_n
        """
        n = self.n
        return n**2 - 1

    def lie_algebra(self):
        """
        Returns the Lie algebra associated with A_n
        """
        n = self.n
        return "su(" + str(n + 1) + ")"

    def dynkin_diagram(self):
        n = self.n
        diag = "---".join("0" for i in range(1, n+1)) + "\n"
        diag += "   ".join(str(i) for i in range(1, n+1))
        return diag
