#!/usr/bin/env python3
"""
打包结果测试脚本
验证所有功能是否正常工作
"""
import os
import sys
import subprocess
import time
import json
from pathlib import Path
from typing import Dict, List, Tuple

class PackageValidator:
    """打包验证器"""
    
    def __init__(self, package_dir: Path):
        self.package_dir = Path(package_dir)
        self.python_exe = self.package_dir / "python" / "python.exe"
        self.app_dir = self.package_dir / "app"
        self.main_py = self.app_dir / "main.py"
        
        self.test_results = []
    
    def log_test(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        status = "[成功] PASS" if success else "[错误] FAIL"
        print(f"{status} {test_name}: {message}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        })
    
    def test_basic_structure(self):
        """测试基本目录结构"""
        print("[检查] 测试基本目录结构...")
        
        required_items = [
            ("python/python.exe", "Python解释器"),
            ("app/main.py", "主程序"),
            ("app/config.yaml", "配置文件"),
            ("app/src/", "源代码目录"),
            ("app/models/", "模型目录"),
            ("app/data/", "数据目录"),
            ("smart_launcher.py", "启动器脚本"),
            ("start_app.bat", "批处理启动器")
        ]
        
        all_passed = True
        for item_path, description in required_items:
            full_path = self.package_dir / item_path
            exists = full_path.exists()
            self.log_test(f"结构检查 - {description}", exists, str(full_path))
            if not exists:
                all_passed = False
        
        return all_passed
    
    def test_python_environment(self):
        """测试Python环境"""
        print("[Python] 测试Python环境...")
        
        if not self.python_exe.exists():
            self.log_test("Python环境", False, "Python解释器不存在")
            return False
        
        try:
            # 测试Python版本
            result = subprocess.run([
                str(self.python_exe), "--version"
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                version = result.stdout.strip()
                self.log_test("Python版本", True, version)
            else:
                self.log_test("Python版本", False, "无法获取版本")
                return False
            
            # 测试关键包导入
            test_imports = [
                "torch",
                "transformers", 
                "sentence_transformers",
                "chromadb",
                "PyQt6",
                "yaml",
                "numpy"
            ]
            
            import_success = True
            for package in test_imports:
                try:
                    result = subprocess.run([
                        str(self.python_exe), "-c", f"import {package}; print(f'{package} OK')"
                    ], capture_output=True, text=True, timeout=30)
                    
                    if result.returncode == 0:
                        self.log_test(f"包导入 - {package}", True, result.stdout.strip())
                    else:
                        self.log_test(f"包导入 - {package}", False, result.stderr.strip())
                        import_success = False
                        
                except subprocess.TimeoutExpired:
                    self.log_test(f"包导入 - {package}", False, "导入超时")
                    import_success = False
            
            return import_success
            
        except Exception as e:
            self.log_test("Python环境", False, str(e))
            return False
    
    def test_model_files(self):
        """测试模型文件"""
        print("[模型] 测试模型文件...")
        
        models_dir = self.app_dir / "models"
        if not models_dir.exists():
            self.log_test("模型目录", False, "模型目录不存在")
            return False
        
        required_models = [
            "qwen-1.5-1.8b-chat",
            "shibing624"
        ]
        
        all_models_ok = True
        for model_name in required_models:
            model_path = models_dir / model_name
            if model_path.exists() and any(model_path.iterdir()):
                # 检查关键文件
                key_files = ["config.json", "pytorch_model.bin", "tokenizer.json"]
                model_files = [f.name for f in model_path.rglob("*") if f.is_file()]
                
                has_key_files = any(kf in " ".join(model_files) for kf in key_files)
                self.log_test(f"模型 - {model_name}", has_key_files, 
                            f"文件数: {len(model_files)}")
                
                if not has_key_files:
                    all_models_ok = False
            else:
                self.log_test(f"模型 - {model_name}", False, "模型文件缺失")
                all_models_ok = False
        
        return all_models_ok
    
    def test_data_integrity(self):
        """测试数据完整性"""
        print("[数据] 测试数据完整性...")
        
        data_dir = self.app_dir / "data"
        if not data_dir.exists():
            self.log_test("数据目录", False, "数据目录不存在")
            return False
        
        # 检查向量数据库
        chroma_db = data_dir / "chroma_db"
        if chroma_db.exists():
            db_files = list(chroma_db.rglob("*"))
            self.log_test("ChromaDB", len(db_files) > 0, f"文件数: {len(db_files)}")
        else:
            self.log_test("ChromaDB", False, "ChromaDB目录不存在")
        
        # 检查全文索引
        whoosh_index = data_dir / "whoosh_index"
        if whoosh_index.exists():
            index_files = list(whoosh_index.rglob("*"))
            self.log_test("Whoosh索引", len(index_files) > 0, f"文件数: {len(index_files)}")
        else:
            self.log_test("Whoosh索引", False, "Whoosh索引目录不存在")
        
        return True
    
    def test_configuration(self):
        """测试配置文件"""
        print("[配置] 测试配置文件...")
        
        config_file = self.app_dir / "config.yaml"
        if not config_file.exists():
            self.log_test("配置文件", False, "config.yaml不存在")
            return False
        
        try:
            import yaml
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 检查关键配置项
            required_keys = [
                "model", "database", "processing", "ui"
            ]
            
            config_ok = True
            for key in required_keys:
                if key in config:
                    self.log_test(f"配置项 - {key}", True, "存在")
                else:
                    self.log_test(f"配置项 - {key}", False, "缺失")
                    config_ok = False
            
            return config_ok
            
        except Exception as e:
            self.log_test("配置文件", False, f"解析失败: {e}")
            return False
    
    def test_launcher(self):
        """测试启动器"""
        print("[启动] 测试启动器...")
        
        launcher_script = self.package_dir / "smart_launcher.py"
        if not launcher_script.exists():
            self.log_test("启动器脚本", False, "smart_launcher.py不存在")
            return False
        
        try:
            # 测试启动器语法
            result = subprocess.run([
                str(self.python_exe), "-m", "py_compile", str(launcher_script)
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                self.log_test("启动器语法", True, "语法检查通过")
            else:
                self.log_test("启动器语法", False, result.stderr.strip())
                return False
            
            # 测试批处理启动器
            batch_launcher = self.package_dir / "start_app.bat"
            if batch_launcher.exists():
                self.log_test("批处理启动器", True, "存在")
            else:
                self.log_test("批处理启动器", False, "start_app.bat不存在")
            
            return True
            
        except Exception as e:
            self.log_test("启动器", False, str(e))
            return False
    
    def test_application_startup(self):
        """测试应用启动（快速测试）"""
        print("[运行] 测试应用启动...")
        
        try:
            # 设置环境变量
            env = os.environ.copy()
            env["PYTHONPATH"] = str(self.app_dir)
            env["APP_ROOT"] = str(self.package_dir)
            
            # 快速启动测试（只检查导入，不启动GUI）
            test_script = f'''
import sys
sys.path.insert(0, r"{self.app_dir}")

try:
    # 测试核心模块导入
    from src.utils.config import get_config
    from src.core.search_system import get_search_system
    from src.ui.main_window import MainWindow
    
    print("IMPORT_SUCCESS")
except Exception as e:
    print(f"IMPORT_ERROR: {{e}}")
'''
            
            result = subprocess.run([
                str(self.python_exe), "-c", test_script
            ], capture_output=True, text=True, timeout=60, env=env, cwd=str(self.app_dir))
            
            if "IMPORT_SUCCESS" in result.stdout:
                self.log_test("应用启动", True, "核心模块导入成功")
                return True
            else:
                error_msg = result.stderr.strip() or result.stdout.strip()
                self.log_test("应用启动", False, error_msg)
                return False
                
        except subprocess.TimeoutExpired:
            self.log_test("应用启动", False, "启动超时")
            return False
        except Exception as e:
            self.log_test("应用启动", False, str(e))
            return False
    
    def generate_test_report(self):
        """生成测试报告"""
        print("[报告] 生成测试报告...")
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        report = f'''# 打包测试报告

## 测试概要
- 总测试数: {total_tests}
- 通过测试: {passed_tests}
- 失败测试: {failed_tests}
- 成功率: {success_rate:.1f}%

## 详细结果
'''
        
        for result in self.test_results:
            status = "[成功]" if result["success"] else "[错误]"
            report += f"- {status} {result['test']}: {result['message']}\\n"
        
        report += f'''
## 测试时间
- 测试时间: {time.strftime("%Y-%m-%d %H:%M:%S")}

## 建议
'''
        
        if failed_tests == 0:
            report += "[完成] 所有测试通过！打包结果可以正常使用。\\n"
        else:
            report += f"⚠️ 有 {failed_tests} 个测试失败，请检查相关问题。\\n"
        
        # 保存报告
        report_file = self.package_dir.parent / "TEST_REPORT.md"
        report_file.write_text(report, encoding='utf-8')
        
        print(f"[成功] 测试报告已生成: {report_file}")
        return success_rate >= 80  # 80%以上通过率认为成功
    
    def run_all_tests(self):
        """运行所有测试"""
        print("[测试] 开始打包验证测试...")
        print("=" * 50)
        
        test_functions = [
            self.test_basic_structure,
            self.test_python_environment,
            self.test_model_files,
            self.test_data_integrity,
            self.test_configuration,
            self.test_launcher,
            self.test_application_startup
        ]
        
        overall_success = True
        for test_func in test_functions:
            try:
                success = test_func()
                if not success:
                    overall_success = False
            except Exception as e:
                print(f"[错误] 测试异常: {test_func.__name__} - {e}")
                overall_success = False
            print()  # 空行分隔
        
        # 生成报告
        report_success = self.generate_test_report()
        
        print("=" * 50)
        if overall_success and report_success:
            print("[完成] 所有测试通过！打包结果验证成功。")
        else:
            print("[错误] 部分测试失败，请检查问题。")
        
        return overall_success and report_success

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python test_package.py <package_directory>")
        print("示例: python test_package.py build_package")
        sys.exit(1)
    
    package_dir = Path(sys.argv[1])
    if not package_dir.exists():
        print(f"[错误] 打包目录不存在: {package_dir}")
        sys.exit(1)
    
    validator = PackageValidator(package_dir)
    success = validator.run_all_tests()
    
    input("\\n按回车键退出...")
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
